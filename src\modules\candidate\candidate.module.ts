import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  CandidateProfile,
  CandidateProfileSchema,
} from './schemas/candidate-profile.schema';
import { CandidateProfileService } from './candidate.service';
import { CandidateProfileController } from './candidate.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: CandidateProfile.name, schema: CandidateProfileSchema },
    ]),
  ],
  controllers: [CandidateProfileController],
  providers: [CandidateProfileService],
  exports: [CandidateProfileService],
})
export class CandidateProfileModule {}
