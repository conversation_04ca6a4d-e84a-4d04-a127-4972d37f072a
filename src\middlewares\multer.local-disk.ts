import multer, { Options } from 'multer';
import path from 'path';
import fs from 'fs';

// Define the local uploads directory
const uploadDir = path.join(process.cwd(), 'uploads');

// Ensure the directory exists
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Define Multer options explicitly
export const diskMulterOptions: Options = {
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, uploadDir); // Store files in 'uploads' directory
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
      console.log('File:', `${uniqueSuffix}-${file.originalname}`);
      cb(null, `${uniqueSuffix}-${file.originalname}`);
    },
  }),
};

// Define the local profile-image uploads directory
const profileImageDir = path.join(uploadDir, 'profile-image');

// Ensure the profile-image directory exists
if (!fs.existsSync(profileImageDir)) {
  fs.mkdirSync(profileImageDir, { recursive: true });
}

// Multer options for profile image uploads
export const profileImageMulterOptions: Options = {
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, profileImageDir); // Store files in 'uploads/profile-image' directory
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
      cb(null, `${uniqueSuffix}-${file.originalname}`);
    },
  }),
};
