import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UseInterceptors,
  UploadedFile,
  UseGuards,
} from '@nestjs/common';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './schemas/user.schema';
import { diskMulterOptions } from '../../middlewares/multer.local-disk';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ERROR } from '../../utils/error-code';

@UseGuards(JwtAuthGuard)
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  async create(
    @Body() createUserDto: CreateUserDto,
  ): Promise<{ message: string; data: User | null; status: number }> {
    const response = await this.userService.create(createUserDto);
    if (response.data) {
      return {
        message: ERROR.USER_ADD_SUCCESS,
        data: response.data,
        status: 201,
      };
    } else {
      return {
        message: ERROR.USER_EXISTS,
        data: null,
        status: 400,
      };
    }
  }

  @Get()
  async findAll(): Promise<{ message: string; data: User[] }> {
    return this.userService.findAll();
  }

  @Get('counts')
  async getUserCounts() {
    const counts = await this.userService.getUserCountsByRole();
    return {
      message: 'User counts by role',
      data: counts,
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<User> {
    return this.userService.findOne(id);
  }

  @Put(':id')
  @UseInterceptors(FileInterceptor('profile', diskMulterOptions))
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @UploadedFile() file: Express.Multer.File,
  ): Promise<{ message: string; data: User | null; status: number }> {
    let filename = '';
    if (file) {
      filename = file.filename;
    }

    const response = await this.userService.update(id, updateUserDto, filename);
    if (response.data) {
      return {
        message: 'User Updated successfully',
        data: response.data,
        status: 201,
      };
    } else {
      return {
        message: 'Email already exists',
        data: null,
        status: 400,
      };
    }
  }

  @Delete(':id')
  async remove(
    @Param('id') id: string,
  ): Promise<{ message: string; data: User }> {
    return this.userService.remove(id);
  }
}
