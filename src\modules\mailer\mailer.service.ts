import { Injectable } from '@nestjs/common';
import { MailerService as NestMailerService } from '@nestjs-modules/mailer';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class MailService {
  constructor(private readonly mailerService: NestMailerService) {}

  async sendResetPasswordMail(to: string, name: string, resetUrl: string) {
    const templatePath = path.join(__dirname, 'reset-password.template.html');
    let html = fs.readFileSync(templatePath, 'utf8');
    html = html
      .replace(/{{name}}/g, name)
      .replace(/{{resetUrl}}/g, resetUrl)
      .replace(/{{year}}/g, new Date().getFullYear().toString());
    await this.mailerService.sendMail({
      to,
      subject: 'Reset Your Password',
      html,
    });
  }

  async requestVerification(email: string, verificationUrl: string) {
    const templatePath = path.join(__dirname, 'verify-domain.template.html');
    let html = fs.readFileSync(templatePath, 'utf8');
    html = html
      .replace(/{{verificationUrl}}/g, verificationUrl)
      .replace(/{{year}}/g, new Date().getFullYear().toString());
    await this.mailerService.sendMail({
      to: email,
      subject: 'Verify your domain',
      html,
    });
    return { message: 'Verification email sent' };
  }
}
