import { Injectable } from '@nestjs/common';
import { Contract, Wallet, JsonRpcProvider, ethers } from 'ethers';
import ResumeVerifierABI from '../../blockchain/ResumeVerifier.json';

@Injectable()
export class BlockchainService {
  private contract: Contract;
  private signer: Wallet;

  constructor() {
    const provider = new JsonRpcProvider(
      process.env.RPC_URL || 'http://127.0.0.1:8545',
    );
    this.signer = new Wallet(
      '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80',
      provider,
    );

    this.contract = new Contract(
      '******************************************', // Replace with your contract address
      ResumeVerifierABI.abi,
      this.signer,
    );
  }

  async storeResumeHash(resume: Record<string, any>) {
    const hash = ethers.keccak256(ethers.toUtf8Bytes(JSON.stringify(resume)));
    const tx = await this.contract.storeResumeHash(hash);
    await tx.wait();
    const userAddress = await this.signer.getAddress();

    return { hash, txHash: tx.hash, userAddress };
  }

  async verifyResume(userAddress: string, resume: Record<string, any>) {
    const hash = ethers.keccak256(ethers.toUtf8Bytes(JSON.stringify(resume)));
    const result = await this.contract.verifyResume(userAddress, hash);
    return result;
  }
}
