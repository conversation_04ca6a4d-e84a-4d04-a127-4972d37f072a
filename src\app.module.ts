// src/app.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from './config/config.module'; // Import ConfigModule here
import { UserModule } from './modules/user/user.module';
import { SeederModule } from './modules/seeder/seeder.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ResponseInterceptor } from './response.interceptor';
import { MiddlewareModule } from './middleware.module';
import { DatabaseModule } from './database/database.module';
import { RoleModule } from './modules/role/role.module';
import { AuthModule } from './modules/auth/auth.module';
import { MailerModule } from './modules/mailer/mailer.module';
import { CandidateProfileModule } from './modules/candidate/candidate.module';
import { AdminProfileModule } from './modules/adminProfile/admin-profile.module';
import { UserActivityModule } from './modules/user-activity/user-activity.module';
import { EmployeeModule } from './modules/employee/employee.module';
import { BlockchainModule } from './modules/blockchain/blockchain.module';

@Module({
  imports: [
    ConfigModule, // Import ConfigModule to use it in Mongoose setup
    DatabaseModule,
    // Import your modules here
    MiddlewareModule,
    UserModule,
    SeederModule,
    RoleModule,
    AuthModule,
    MailerModule,
    CandidateProfileModule,
    AdminProfileModule,
    UserActivityModule,
    EmployeeModule,
    BlockchainModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
  ],
})
export class AppModule {}
