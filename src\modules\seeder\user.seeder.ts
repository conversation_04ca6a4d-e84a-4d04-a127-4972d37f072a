import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from '../user/schemas/user.schema';
import { Role } from '../role/schemas/role.schema';
import * as bcrypt from 'bcrypt';
import { AdminProfile } from '../adminProfile/schemas/admin-profile.schema';
import { ROLE } from 'src/utils/constants';

@Injectable()
export class UserSeeder {
  constructor(
    @InjectModel('User') private readonly userModel: Model<User>,
    @InjectModel('Role') private readonly roleModel: Model<Role>,
    @InjectModel('AdminProfile')
    private readonly adminProfileModel: Model<AdminProfile>,
  ) {}

  async seedAdminUser() {
    // Ensure admin role exists
    const adminRole = await this.roleModel.findOne({ name: 'Admin' });

    // Check if admin user exists
    const adminEmail = '<EMAIL>';
    let adminUser = await this.userModel.findOne({ email: adminEmail });
    if (!adminUser) {
      const hashedPassword = await bcrypt.hash('Admin@123', 10);
      adminUser = await this.userModel.create({
        email: adminEmail,
        password: hashedPassword,
        role: adminRole._id,
        firstName: ROLE.ADMIN,
        lastName: 'test',
        isActive: true,
        // Add other required fields as per your schema
      });
      // Create admin profile for the admin user
      await this.adminProfileModel.create({
        userId: adminUser._id,
        image: 'default-admin.jpg',
      });
      console.log('Admin user and profile created:', adminUser);
    } else {
      console.log('Admin user already exists');
    }
  }
}
