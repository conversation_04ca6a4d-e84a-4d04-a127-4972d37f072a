import { IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsMongoId } from 'class-validator';

export class CreateUserDto {
  @IsString()
  @MinLength(1)
  firstName: string;

  @IsString()
  @MinLength(1)
  lastName: string;

  @IsString()
  @MinLength(5)
  email: string;

  @IsNotEmpty()
  @IsMongoId()
  role: string;

  @IsString()
  @IsNotEmpty()
  password?: string;
}
