import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import type { CandidateProfile } from '@/types/candidateProfile';

interface CertificationsTabProps {
  certifications: CandidateProfile['certifications'];
  editing: boolean;
  handleArrayAdd: (
    field: 'certifications',
    emptyObj: Record<string, unknown>
  ) => void;
  handleArrayRemove: (field: 'certifications', idx: number) => void;
  handleArrayChange: (
    field: 'certifications',
    idx: number,
    subfield: string,
    value: string | number | boolean
  ) => void;
}

const CertificationsTab: React.FC<CertificationsTabProps> = ({
  certifications,
  editing,
  handleArrayAdd,
  handleArrayRemove,
  handleArrayChange,
}) => {
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <div className="font-semibold text-[#193E6C]">Certifications</div>
        {editing && (
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              handleArrayAdd('certifications', {
                name: '',
                authority: '',
                licenseNumber: '',
                year: '',
              })
            }
          >
            <Plus className="w-4 h-4" /> Add
          </Button>
        )}
      </div>
      <div className="space-y-4">
        {(certifications ?? []).map((cert, idx) => (
          <div
            key={idx}
            className="bg-white rounded-lg p-4 flex flex-col gap-2 relative"
          >
            {editing && (
              <button
                className="absolute top-2 right-2 text-red-500"
                onClick={() => handleArrayRemove('certifications', idx)}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            )}
            <input
              className="input"
              placeholder="Certification Name"
              value={cert.name}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange('certifications', idx, 'name', e.target.value)
              }
            />
            <input
              className="input"
              placeholder="Authority"
              value={cert.authority}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange(
                  'certifications',
                  idx,
                  'authority',
                  e.target.value
                )
              }
            />
            <input
              className="input"
              placeholder="License Number"
              value={cert.licenseNumber}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange(
                  'certifications',
                  idx,
                  'licenseNumber',
                  e.target.value
                )
              }
            />
            <input
              className="input"
              placeholder="Year"
              value={cert.year}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange('certifications', idx, 'year', e.target.value)
              }
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default CertificationsTab;
