import React from 'react';
import type { CandidateProfile } from '@/types/candidateProfile';

interface ErrorFields {
  fullName?: string;
  email?: string;
  currentJobTitle?: string;
  phoneNumber?: string;
  permanentAddress?: string;
  postalCode?: string;
}

interface BasicInformationTabProps {
  form: CandidateProfile;
  editing: boolean;
  errors: ErrorFields;
  handleChange: (
    field: keyof CandidateProfile,
    value:
      | string
      | number
      | boolean
      | import('@/types/candidateProfile').StaticProfile
  ) => void;
}

const BasicInformationTab: React.FC<BasicInformationTabProps> = ({
  form,
  editing,
  errors,
  handleChange,
}) => (
  <div className="space-y-6">
    {/* Personal Info */}
    <div>
      <div className="font-semibold text-[#193E6C] mb-2">Personal Info</div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
        <div>
          <label className="block text-xs text-[#193E6C] mb-1">
            Full Name *
          </label>
          <div className="flex items-center gap-2">
            <input
              value={form.fullName}
              readOnly={!editing}
              onChange={e => handleChange('fullName', e.target.value)}
              className="w-full bg-[#E3F0FF] rounded-lg px-4 py-2 text-[#193E6C] font-medium"
            />
          </div>
          {errors.fullName && (
            <div className="text-red-600 text-xs mt-1">{errors.fullName}</div>
          )}
        </div>
        <div>
          <label className="block text-xs text-[#193E6C] mb-1">
            Current Job Title *
          </label>
          <div className="flex items-center gap-2">
            <input
              value={form.currentJobTitle}
              readOnly={!editing}
              onChange={e => handleChange('currentJobTitle', e.target.value)}
              className="w-full bg-[#E3F0FF] rounded-lg px-4 py-2 text-[#193E6C] font-medium"
            />
          </div>
          {errors.currentJobTitle && (
            <div className="text-red-600 text-xs mt-1">
              {errors.currentJobTitle}
            </div>
          )}
        </div>
        <div>
          <label className="block text-xs text-[#193E6C] mb-1">
            Email Address *
          </label>
          <div className="flex items-center gap-2">
            <input
              value={form.email}
              readOnly
              className="w-full bg-[#E3F0FF] rounded-lg px-4 py-2 text-[#193E6C] font-medium"
            />
          </div>
          {errors.email && (
            <div className="text-red-600 text-xs mt-1">{errors.email}</div>
          )}
        </div>
        <div>
          <label className="block text-xs text-[#193E6C] mb-1">
            Phone Number *
          </label>
          <div className="flex items-center gap-2">
            <input
              value={form.phoneNumber}
              readOnly={!editing}
              onChange={e => handleChange('phoneNumber', e.target.value)}
              className="w-full bg-[#E3F0FF] rounded-lg px-4 py-2 text-[#193E6C] font-medium"
            />
          </div>
          {errors.phoneNumber && (
            <div className="text-red-600 text-xs mt-1">
              {errors.phoneNumber}
            </div>
          )}
        </div>
      </div>
    </div>
    {/* Location */}
    <div>
      <div className="font-semibold text-[#193E6C] mb-2">Location</div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
        <div>
          <label className="block text-xs text-[#193E6C] mb-1">
            Permanent Address *
          </label>
          <div className="flex items-center gap-2">
            <input
              value={form.permanentAddress}
              readOnly={!editing}
              onChange={e => handleChange('permanentAddress', e.target.value)}
              className="w-full bg-[#E3F0FF] rounded-lg px-4 py-2 text-[#193E6C] font-medium"
            />
          </div>
          {errors.permanentAddress && (
            <div className="text-red-600 text-xs mt-1">
              {errors.permanentAddress}
            </div>
          )}
        </div>
        <div>
          <label className="block text-xs text-[#193E6C] mb-1">
            Postal Code *
          </label>
          <div className="flex items-center gap-2">
            <input
              value={form.postalCode || ''}
              readOnly={!editing}
              onChange={e => handleChange('postalCode', e.target.value)}
              className="w-full bg-[#E3F0FF] rounded-lg px-4 py-2 text-[#193E6C] font-medium"
            />
          </div>
          {errors.postalCode && (
            <div className="text-red-600 text-xs mt-1">{errors.postalCode}</div>
          )}
        </div>
      </div>
    </div>
    {/* Bio */}
    <div>
      <div className="font-semibold text-[#193E6C] mb-2">Bio</div>
      <div className="flex items-center gap-2">
        <textarea
          value={form.bio}
          readOnly={!editing}
          onChange={e => handleChange('bio', e.target.value)}
          className="w-full bg-[#E3F0FF] rounded-lg px-4 py-2 text-[#193E6C] font-medium min-h-[60px]"
        />
      </div>
    </div>
  </div>
);

export default BasicInformationTab;
