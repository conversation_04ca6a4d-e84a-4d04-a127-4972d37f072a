'use client';
import React, { useState } from 'react';
import StatsCard from '@/components/applications/StatsCard';
import ApplicationCard from '@/components/applications/ApplicationCard';

// Mock Data
const stats = [
  { label: 'Total Applied', value: 12, icon: '👤' },
  { label: 'Interviews', value: 3, icon: '✉️' },
  { label: 'Offers', value: 1, icon: '📈' },
  { label: 'Rejected', value: 6, icon: '📄' },
];

const applications = [
  {
    id: 1,
    title: 'Senior Frontend Developer',
    status: 'Interview',
    company: 'TechCorp Inc.',
    location: 'San Francisco, CA',
    appliedDate: '1/15/2024',
    salary: '$120,000 - $150,000',
    progress: 60,
    progressLabel: 'Technical Interview',
    interviewers: [
      { name: '<PERSON>', role: 'Engineering Manager' },
      { name: '<PERSON>', role: 'Senior Developer' },
    ],
    viewJobUrl: '#',
    viewDetailsUrl: '#',
  },
  {
    id: 2,
    title: 'Full Stack Engineer',
    status: 'Offer',
    company: 'StartupXYZ',
    location: 'Remote',
    appliedDate: '1/15/2024',
    salary: '$120,000 - $150,000',
    progress: 100,
    progressLabel: 'Offer Received',
    interviewers: [{ name: '<PERSON> Doe', role: 'CTO' }],
    viewJobUrl: '#',
    viewDetailsUrl: '#',
  },
];

const statusOptions = ['All Status', 'Interview', 'Offer', 'Rejected'];

export default function MyApplicationsPage() {
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState('All Status');

  const filteredApps = applications.filter(app => {
    const matchesSearch =
      app.title.toLowerCase().includes(search.toLowerCase()) ||
      app.company.toLowerCase().includes(search.toLowerCase()) ||
      app.location.toLowerCase().includes(search.toLowerCase());
    const matchesStatus =
      status === 'All Status' ? true : app.status === status;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-100 to-white py-10 px-2 sm:px-4 md:px-12">
      <h1 className="text-3xl font-bold mb-8">My Applications</h1>
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mb-8">
        {stats.map(s => (
          <StatsCard key={s.label} {...s} />
        ))}
      </div>
      {/* Search & Filter Bar */}
      <div className="bg-white rounded-xl shadow flex flex-col sm:flex-row items-center p-4 mb-8 gap-4">
        <div className="flex-1 w-full flex items-center">
          <span className="mr-2 text-gray-400 text-xl">🔍</span>
          <input
            type="text"
            placeholder="Search Applications"
            value={search}
            onChange={e => setSearch(e.target.value)}
            className="w-full border-none outline-none bg-transparent text-gray-700 text-sm"
          />
        </div>
        <select
          className="border border-gray-200 rounded-lg px-4 py-2 text-sm text-gray-700"
          value={status}
          onChange={e => setStatus(e.target.value)}
        >
          {statusOptions.map(opt => (
            <option key={opt}>{opt}</option>
          ))}
        </select>
        <button className="bg-white border border-blue-600 text-blue-600 rounded-lg px-4 py-2 hover:bg-blue-50 text-sm font-medium transition-colors">
          + Add Applications
        </button>
      </div>
      {/* Application Cards */}
      <div>
        {filteredApps.length === 0 ? (
          <div className="text-center text-gray-400 py-16">
            <div className="text-5xl mb-4">📭</div>
            <div className="text-lg font-medium mb-2">
              No applications found
            </div>
            <div className="text-sm">
              Try adjusting your search or filter criteria.
            </div>
          </div>
        ) : (
          filteredApps.map(app => <ApplicationCard key={app.id} app={app} />)
        )}
      </div>
    </div>
  );
}
