import { NestFactory } from '@nestjs/core';
import { SeederModule } from './modules/seeder/seeder.module';
import { RoleSeeder } from './modules/seeder/role.seeder';
import { UserSeeder } from './modules/seeder/user.seeder';

async function bootstrap() {
  const appContext = await NestFactory.createApplicationContext(SeederModule);

  try {
    const roleSeeder = appContext.get(RoleSeeder);
    await roleSeeder.seed();
    const userSeeder = appContext.get(UserSeeder);
    await userSeeder.seedAdminUser();
    console.log('Seeding completed successfully');
  } catch (error) {
    console.error('Seeding failed:', error);
  } finally {
    await appContext.close();
  }
}

void bootstrap();
