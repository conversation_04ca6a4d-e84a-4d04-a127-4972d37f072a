import React from 'react';

interface ProfileTabsProps {
  tabs: string[];
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const ProfileTabs: React.FC<ProfileTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
}) => {
  return (
    <div className="flex gap-2 mb-6 overflow-x-auto">
      {tabs.map(tab => (
        <button
          key={tab}
          onClick={() => onTabChange(tab)}
          className={`px-6 py-2 rounded-full font-semibold transition-colors whitespace-nowrap ${activeTab === tab ? 'bg-[#3C9DF6] text-white shadow' : 'bg-[#F6F9FF] text-[#193E6C] hover:bg-[#E3F0FF]'}`}
        >
          {tab}
        </button>
      ))}
    </div>
  );
};

export default ProfileTabs;
