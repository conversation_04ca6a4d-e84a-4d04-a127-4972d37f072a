# .github/workflows/eslint.yml
name: ESLint Check

on:
  pull_request:
    branches: [dev]

jobs:
  eslint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10.6.4 # Use your actual pnpm version

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        working-directory: ./

      - name: Run ESLint
        run: pnpm lint:show
