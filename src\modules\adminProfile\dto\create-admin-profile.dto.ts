import {
  IsMongoId,
  IsOptional,
  IsString,
  IsEmail,
  IsBoolean,
} from 'class-validator';

export class CreateAdminProfileDto {
  // User fields
  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsEmail()
  email: string;

  @IsString()
  password: string;

  @IsMongoId()
  role: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  // AdminProfile fields
  @IsMongoId()
  userId?: string;

  @IsOptional()
  @IsString()
  image?: string;
}
