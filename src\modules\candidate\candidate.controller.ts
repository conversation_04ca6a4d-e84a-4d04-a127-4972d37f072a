import {
  Body,
  Controller,
  Post,
  Patch,
  Param,
  UsePipes,
  ValidationPipe,
  Get,
  UseGuards,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { CandidateProfileService } from './candidate.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { profileImageMulterOptions } from '../../middlewares/multer.local-disk';
import { CreateCandidateProfileDto } from './dto/create-candidate-profile.dto';

@UseGuards(JwtAuthGuard)
@Controller('candidate-profile')
export class CandidateProfileController {
  constructor(
    private readonly candidateProfileService: CandidateProfileService,
  ) {}

  @Post(':userId')
  @UseInterceptors(FileInterceptor('image', profileImageMulterOptions))
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async create(
    @Param('userId') userId: string,
    @Body() createDto: CreateCandidateProfileDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    const profile = await this.candidateProfileService.create(
      userId,
      createDto,
      file,
    );
    return {
      statusCode: 201,
      message: 'Candidate profile created',
      data: profile,
    };
  }

  @Patch(':id')
  @UseInterceptors(FileInterceptor('image', profileImageMulterOptions))
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async update(
    @Param('id') id: string,
    @Body() updateDto: CreateCandidateProfileDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    const profile = await this.candidateProfileService.update(
      id,
      updateDto,
      file,
    );
    return {
      statusCode: 200,
      message: 'Candidate profile updated',
      data: profile,
    };
  }

  @Get('user/:userId')
  async getByUserId(@Param('userId') userId: string) {
    const profile = await this.candidateProfileService.findByUserId(userId);
    if (!profile) {
      return {
        statusCode: 404,
        message: 'Candidate profile not found',
        data: null,
      };
    }
    return {
      statusCode: 200,
      message: 'Candidate profile found',
      data: profile,
    };
  }

  @Get(':userId/completion')
  async getProfileCompletion(@Param('userId') userId: string) {
    const profile = await this.candidateProfileService.findByUserId(userId);
    if (!profile) {
      return {
        statusCode: 404,
        message: 'Candidate profile not found',
        data: null,
      };
    } else {
      const completion =
        this.candidateProfileService.getProfileCompletion(profile);
      return {
        statusCode: 200,
        message: 'Candidate profile found',
        data: completion,
      };
    }
  }
}
