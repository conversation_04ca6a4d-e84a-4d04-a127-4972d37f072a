import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { Request } from 'express';
import * as useragent from 'useragent';
import { UserActivityService } from './user-activity.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { UserActivityDto } from './dto/create-user-activity.dto';

@Controller('user-activity')
export class UserActivityController {
  constructor(private readonly userActivityService: UserActivityService) {}

  @UseGuards(JwtAuthGuard)
  @Post('/user-activity/add')
  async userActivity(@Body() request: UserActivityDto, @Req() req: Request) {
    const origin = req.headers['origin'] || req.headers['referer'] || 'Unknown';
    const endpoint = req.originalUrl;
    const userAgent = req.headers['user-agent'] || '';
    const agent = useragent.parse(userAgent);
    const ipAddress =
      req.headers['x-forwarded-for'] || req.socket.remoteAddress;
    const clientIp = Array.isArray(ipAddress) ? ipAddress[0] : ipAddress;

    const headerDetails = {
      browser: agent.family,
      source: agent.source,
      endpoint,
      origin,
      osName: agent.os.family,
      osVersion: agent.os.toVersion(),
      agentVersion: agent.toVersion(),
      ipAddress: clientIp,
    };

    const userId = (req.user as { userId: string }).userId;
    const response = await this.userActivityService.userActivity(
      request,
      userId,
      headerDetails,
    );
    return { data: response };
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async getAllUserActivity(@Req() req: Request) {
    const userId = (req.user as { userId: string }).userId;
    return await this.userActivityService.getActivitiesByUserId(userId);
  }
}
