'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Code, <PERSON>Check, BarChart3 } from 'lucide-react';

export function FeatureGrid() {
  const features = [
    {
      icon: Code,
      title: 'AI-Powered Matching',
      description:
        'AI-powered algorithm matches candidates with their perfect roles based on skills, culture fit, and preferences.',
      color: 'text-blue-600',
    },
    {
      icon: ShieldCheck,
      title: 'Blockchain Verified',
      description:
        'All candidate profiles are blockchain-verified for authenticity and trust',
      color: 'text-green-600',
    },
    {
      icon: BarChart3,
      title: 'Smart Analytics',
      description:
        'Track applications, interview stages, and get insights to improve your success rate',
      color: 'text-purple-600',
    },
  ];

  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
      {features.map((feature, index) => {
        const IconComponent = feature.icon;
        return (
          <Card
            key={index}
            className="border-0 shadow-lg hover:shadow-xl transition-shadow"
          >
            <CardHeader className="text-center">
              <IconComponent
                className={`h-12 w-12 ${feature.color} mx-auto mb-4`}
              />
              <CardTitle>{feature.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-600 dark:text-slate-300 text-center">
                {feature.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
