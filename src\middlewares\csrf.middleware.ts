import { Injectable, NestMiddleware, Inject } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { createDoubleCsrfOptions } from '../utils/helper.util';
import { ConfigService } from '@nestjs/config';
import { doubleCsrf } from 'csrf-csrf';

@Injectable()
export class CsrfMiddleware implements NestMiddleware {
  private generateCsrfToken: (req: Request, res: Response) => string;
  private doubleCsrfProtection: (
    req: Request,
    res: Response,
    next: NextFunction,
  ) => void;

  constructor(
    @Inject(ConfigService) private readonly configService: ConfigService,
  ) {
    const doubleCsrfOptions = createDoubleCsrfOptions(this.configService);
    const csrf = doubleCsrf(doubleCsrfOptions);
    this.generateCsrfToken = csrf.generateCsrfToken;
    this.doubleCsrfProtection = csrf.doubleCsrfProtection;
  }

  use(req: Request, res: Response, next: NextFunction): void {
    let csrfToken = req.cookies['x-csrf-token'];

    console.log(
      'Generated CSRF Token:',
      JSON.stringify(req.cookies['x-csrf-token']),
    );
    console.log(
      'Received CSRF Token:',
      JSON.stringify(req.headers['x-csrf-token']),
    );

    // Only generate a new token if it's missing
    if (!csrfToken) {
      csrfToken = this.generateCsrfToken(req, res);
      const domain = this.configService.get<string>('app.domainName');
      const env = this.configService.get<string>('app.env');
      res.cookie('XSRF-TOKEN', csrfToken, {
        httpOnly: false,
        secure: ['production', 'development'].includes(env),
        sameSite: ['production', 'development'].includes(env)
          ? 'lax'
          : 'strict',
        domain,
      });
    }
    this.doubleCsrfProtection(req, res, next);
  }
}
