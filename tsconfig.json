{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false}, "include": ["src/**/*.ts", "test/**/*.ts", "configuration.ts"], "exclude": ["node_modules", "dist", "config"]}