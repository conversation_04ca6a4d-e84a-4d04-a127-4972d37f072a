import {
  IsString,
  IsEmail,
  IsOptional,
  IsArray,
  ValidateNested,
  IsBoolean,
  IsDate,
  IsNumber,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

class EducationDto {
  @IsString()
  institution: string;

  @IsOptional()
  @IsString()
  degree?: string;

  @IsOptional()
  @IsString()
  fieldOfStudy?: string;

  @IsOptional()
  @IsString()
  startYear?: string;

  @IsOptional()
  @IsString()
  endYear?: string;

  @IsOptional()
  @IsString()
  grade?: string;

  @IsOptional()
  @IsString()
  country?: string;
}

class WorkExperienceDto {
  @IsString()
  company: string;

  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  from?: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  to?: Date;

  @IsOptional()
  @IsBoolean()
  currentlyWorking?: boolean;

  @IsOptional()
  @IsString()
  description?: string;
}

class CertificationDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  authority?: string;

  @IsOptional()
  @IsString()
  licenseNumber?: string;

  @IsOptional()
  @IsString()
  year?: string;
}

export class StaticProfileDto {
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  technical_skills?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  soft_skills?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  experience?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  cultural_fit?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  total_static_score?: number;
}

export class CreateCandidateProfileDto {
  @IsString()
  fullName: string;

  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @IsOptional()
  @IsString()
  currentJobTitle?: string;

  @IsOptional()
  @IsString()
  permanentAddress?: string;

  @IsOptional()
  @IsString()
  country?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  postalCode?: string;

  @IsOptional()
  @IsString()
  bio?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EducationDto)
  educations?: EducationDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkExperienceDto)
  workExperience?: WorkExperienceDto[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  skills?: string[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CertificationDto)
  certifications?: CertificationDto[];

  @IsOptional()
  @IsString()
  image?: string;

  @IsOptional()
  @IsString()
  resume?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => StaticProfileDto)
  static_profile?: StaticProfileDto;
}
