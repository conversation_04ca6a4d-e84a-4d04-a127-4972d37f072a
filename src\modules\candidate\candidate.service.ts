import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  CandidateProfile,
  CandidateProfileDocument,
} from './schemas/candidate-profile.schema';
import {
  CreateCandidateProfileDto,
  StaticProfileDto,
} from './dto/create-candidate-profile.dto';
import { ConfigService } from '@nestjs/config';
import { ERROR } from '../../utils/error-code';

@Injectable()
export class CandidateProfileService {
  constructor(
    @InjectModel(CandidateProfile.name)
    private readonly candidateProfileModel: Model<CandidateProfileDocument>,
    private readonly configService: ConfigService,
  ) {}

  async create(
    userId: string,
    createDto: CreateCandidateProfileDto,
    file?: Express.Multer.File,
  ): Promise<CandidateProfile> {
    try {
      const parsedData = this.parseProfileDto(createDto, file);
      const staticProfile = this.calculateStaticProfile(
        parsedData.static_profile as unknown as StaticProfileDto,
      );

      const createdProfile = new this.candidateProfileModel({
        ...parsedData,
        userId: new Types.ObjectId(userId),
        static_profile: staticProfile,
      });

      const savedProfile = await createdProfile.save();
      return this.formatProfile(savedProfile);
    } catch (error) {
      console.error('Create profile error:', error);
      throw new NotFoundException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async update(
    id: string,
    updateDto: CreateCandidateProfileDto,
    file?: Express.Multer.File,
  ): Promise<CandidateProfile> {
    try {
      const parsedData = this.parseProfileDto(updateDto, file);

      if (updateDto.static_profile) {
        parsedData.static_profile = this.calculateStaticProfile(
          updateDto.static_profile,
        );
      }

      const updated = await this.candidateProfileModel.findByIdAndUpdate(
        id,
        parsedData,
        { new: true },
      );

      if (!updated)
        throw new NotFoundException(ERROR.CANDIDATE_PROFILE_NOT_FOUND);
      return this.formatProfile(updated);
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      console.error('Update profile error:', error);
      throw new NotFoundException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async findByUserId(userId: string): Promise<CandidateProfile | null> {
    try {
      const objectId = new Types.ObjectId(userId);
      const profile = await this.candidateProfileModel.findOne({
        userId: objectId,
      });
      return profile ? this.formatProfile(profile) : null;
    } catch (error) {
      console.error('Find profile by userId error:', error);
      throw new NotFoundException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async findImageByUserId(userId: string): Promise<string | null> {
    try {
      const objectId = new Types.ObjectId(userId);
      const profile = await this.candidateProfileModel.findOne(
        { userId: objectId },
        { image: 1, _id: 0 },
      );
      return profile?.image ? this.getImageUrl(profile.image) : null;
    } catch (error) {
      console.error('Find image by userId error:', error);
      throw new NotFoundException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async findAllUsersFromCandidateProfiles(): Promise<any[]> {
    try {
      const profiles = await this.candidateProfileModel
        .find()
        .populate('userId');
      return profiles.map((p) => p.userId).filter(Boolean);
    } catch (error) {
      console.error('Find all users error:', error);
      throw new NotFoundException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  getProfileCompletion(profile: CandidateProfile) {
    try {
      const completion = {
        setupAccount: 0,
        personalInfo: 0,
        skillsAndTechnology: 0,
        workExperience: 0,
        certifications: 0,
        education: 0,
      };

      if (profile.email) completion.setupAccount += 6;
      if (profile.fullName) completion.setupAccount += 6;
      if (profile.phoneNumber) completion.setupAccount += 6;

      if (profile.currentJobTitle) completion.personalInfo += 3;
      if (profile.permanentAddress) completion.personalInfo += 3;
      if (profile.country) completion.personalInfo += 3;
      if (profile.city) completion.personalInfo += 3;
      if (profile.postalCode) completion.personalInfo += 3;
      if (profile.bio) completion.personalInfo += 3;

      if (profile.skills?.length) completion.skillsAndTechnology = 9;
      if (
        profile.workExperience?.some(
          (workExperience) => workExperience.company && workExperience.title,
        )
      )
        completion.workExperience = 27;
      if (profile.certifications?.some((certifications) => certifications.name))
        completion.certifications = 18;
      if (profile.educations?.some((educations) => educations.institution))
        completion.education = 10;

      const completionPercentage = Object.values(completion).reduce(
        (indexOne, indexTwo) => indexOne + indexTwo,
        0,
      );
      return { userId: profile.userId, completion, completionPercentage };
    } catch (error) {
      console.error('Profile completion error:', error);
      throw new NotFoundException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  private parseProfileDto(
    dto: CreateCandidateProfileDto,
    file?: Express.Multer.File,
  ) {
    return {
      ...dto,
      educations: this.parseIfString(dto.educations),
      workExperience: this.parseIfString(dto.workExperience),
      certifications: this.parseIfString(dto.certifications),
      image: file?.filename || dto.image,
    };
  }

  private calculateStaticProfile(
    staticProfile: StaticProfileDto,
  ): StaticProfileDto {
    const {
      technical_skills = 0,
      soft_skills = 0,
      experience = 0,
      cultural_fit = 0,
    } = staticProfile || {};

    const total_static_score = Math.min(
      technical_skills + soft_skills + experience + cultural_fit,
      100,
    );

    return {
      technical_skills,
      soft_skills,
      experience,
      cultural_fit,
      total_static_score,
    };
  }

  private parseIfString(val: any) {
    if (typeof val === 'string') {
      try {
        return JSON.parse(val);
      } catch {
        return val;
      }
    }
    return val;
  }

  private formatProfile(profile: CandidateProfile): CandidateProfile {
    return {
      ...profile,
      image: this.getImageUrl(profile.image),
    };
  }

  private getImageUrl(image?: string | null): string | null {
    if (!image) return null;
    return image.startsWith('http')
      ? image
      : `${this.configService.get('app.backendUrl')}/uploads/profile-image/${image}`;
  }
}
