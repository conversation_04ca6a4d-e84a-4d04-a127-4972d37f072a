import React, { useState, useEffect, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchSkills } from '@/store/slices/skillsSlice';

function debounce<T extends (...args: any[]) => void>(func: T, wait: number) {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

interface SkillsInputProps {
  value?: string[];
  onChange?: (skills: string[]) => void;
}

export const SkillsInput: React.FC<SkillsInputProps> = ({
  value = [],
  onChange,
}) => {
  const selectedSkills = value;
  const [input, setInput] = useState('');
  const [query, setQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLUListElement>(null);

  // Debounce query update
  const debouncedSetQuery = useRef(
    debounce((val: string) => setQuery(val), 400)
  ).current;

  const dispatch = useAppDispatch();
  const { data: suggestions, loading: isFetching } = useAppSelector(
    state => state.skills
  );

  useEffect(() => {
    if (input.trim()) {
      debouncedSetQuery(input);
      setShowDropdown(true);
    } else {
      setShowDropdown(false);
    }
  }, [input, debouncedSetQuery]);

  useEffect(() => {
    if (query) {
      dispatch(fetchSkills(query));
    }
  }, [query, dispatch]);

  const handleSelect = (skill: string) => {
    if (!selectedSkills.includes(skill)) {
      onChange?.([...selectedSkills, skill]);
    }
    setInput('');
    setShowDropdown(false);
    inputRef.current?.focus();
  };

  const handleRemove = (skill: string) => {
    onChange?.(selectedSkills.filter(s => s !== skill));
  };

  // Prevent dropdown from closing before click is registered
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="w-full max-w-lg relative">
      <div className="flex flex-wrap gap-2 mb-2">
        {selectedSkills.map((skill, idx) => (
          <span
            key={skill + '-' + idx}
            className="bg-blue-100 text-blue-800 px-2 py-1 rounded flex items-center"
          >
            {skill}
            <button
              type="button"
              className="ml-1 text-xs text-red-500 hover:text-red-700"
              onClick={() => handleRemove(skill)}
            >
              ×
            </button>
          </span>
        ))}
      </div>
      <input
        ref={inputRef}
        type="text"
        className="border px-3 py-2 rounded w-full"
        placeholder="Type a skill..."
        value={input}
        onChange={e => setInput(e.target.value)}
        onFocus={() => input && setShowDropdown(true)}
        autoComplete="off"
      />
      {showDropdown && (suggestions.length > 0 || isFetching) && (
        <ul
          ref={dropdownRef}
          className="border rounded mt-1 bg-white shadow max-h-48 overflow-y-auto absolute left-0 right-0 z-10"
        >
          {suggestions.map((skill, idx) => (
            <li
              key={skill + '-' + idx}
              className="px-4 py-2 hover:bg-blue-100 cursor-pointer"
              onMouseDown={() => handleSelect(skill)}
            >
              {skill}
            </li>
          ))}
          {isFetching && (
            <li className="px-4 py-2 text-sm text-gray-500">Loading...</li>
          )}
        </ul>
      )}
    </div>
  );
};

export default SkillsInput;
