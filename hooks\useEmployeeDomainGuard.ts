import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAppDispatch, useAppSelector } from '@/store';
import { getDomainVerificationStatus } from '@/store/slices/employeeSlice';
import { getProfile } from '@/store/slices/authSlice';

/**
 * useEmployeeDomainGuard
 *
 * Checks employee's domain verification status and provides a helper to trigger status check/redirect.
 *
 * Usage: Call this hook at the top of a page/component you want to guard.
 *
 * Returns: { domainStatus, isLoading, checkDomainStatus }
 */
export function useEmployeeDomainGuard() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const user = useAppSelector((state: any) => state.auth.user);
  const profileLoading = useAppSelector((state: any) => state.auth.isLoading);
  const domainStatusData = useAppSelector(
    (state: any) => state.employee.domainVerificationStatus
  );
  const domainStatusLoading = useAppSelector(
    (state: any) => state.employee.loading
  );

  const [domainStatus, setDomainStatus] = useState<
    'unknown' | 'verified' | 'unverified' | 'not-employee'
  >('unknown');

  useEffect(() => {
    // Ensure user profile is loaded
    if (!user && !profileLoading) {
      dispatch(getProfile());
    }
  }, [user, profileLoading, dispatch]);

  // Helper to check domain status and optionally redirect
  const checkDomainStatus = useCallback(
    async (redirects = true) => {
      if (profileLoading) return;
      if (user?.roleName === 'employee') {
        try {
          await dispatch(getDomainVerificationStatus()).unwrap();
          if (domainStatusData?.domainVerified === true) {
            setDomainStatus('verified');
            if (redirects) router.replace('/dashboard');
          } else {
            setDomainStatus('unverified');
            if (redirects) router.replace('/request-domain-verification');
          }
        } catch {
          setDomainStatus('unknown');
        }
      } else {
        setDomainStatus('not-employee');
      }
    },
    [profileLoading, user, dispatch, router, domainStatusData]
  );

  // Optionally, auto-check on mount
  useEffect(() => {
    if (!profileLoading && user?.roleName === 'employee' && domainStatusData) {
      if (domainStatusData.domainVerified === true) {
        setDomainStatus('verified');
      } else {
        setDomainStatus('unverified');
      }
    } else if (!profileLoading && user && user.roleName !== 'employee') {
      setDomainStatus('not-employee');
    }
  }, [user, profileLoading, domainStatusData]);

  return {
    domainStatus,
    isLoading: domainStatusLoading || profileLoading,
    checkDomainStatus,
  };
}
