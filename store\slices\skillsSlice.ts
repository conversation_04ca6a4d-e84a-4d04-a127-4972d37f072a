import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { SkillsState } from '../../types/skills';

import api from '@/lib/api';

const APILAYER_BASE_URL =
  process.env.NEXT_PUBLIC_APILAYER_BASE_URL || 'https://api.apilayer.com';
const APILAYER_KEY = process.env.NEXT_PUBLIC_APILAYER_KEY || '';

const initialState: SkillsState = {
  data: [],
  loading: false,
  error: null,
};

export const fetchSkills = createAsyncThunk<
  string[],
  string,
  { rejectValue: string }
>('skills/fetchSkills', async (query, { rejectWithValue }) => {
  try {
    const { data } = await api.get(`${APILAYER_BASE_URL}/skills?q=${query}`, {
      headers: { apikey: APILAYER_KEY },
    });
    // If the API returns an array, return it, else []
    return Array.isArray(data) ? data : [];
  } catch (err: any) {
    return rejectWithValue(err.message);
  }
});

const skillsSlice = createSlice({
  name: 'skills',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchSkills.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSkills.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchSkills.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch skills';
      });
  },
});

export default skillsSlice.reducer;
