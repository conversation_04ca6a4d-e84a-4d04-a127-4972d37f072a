database:
  mongodb:
    uri: 'mongodb://127.0.0.1:27017/talentloop' # Overridden by env-specific files
    dbName: 'talentloop'
    poolSize: 10
    timeoutMS: 5000
    socketTimeoutMS: 45000
    retryAttempts: 3
    retryDelay: 1000
    autoIndex: true # Disable in production
encryption:
  secretKey: 'talentloop' # Secret key for encryption
jwt:
  secret: 'talentloop' # Secret key for JWT signing
  expiresIn: '1h' # Token expiration time in seconds
  resetSecret: 'talentloop-reset' # Secret key for reset token signing
  resetExpiresIn: '1h' # Reset token expiration time in seconds
mail:
  host: smtp.gmail.com
  port: 465
  secure: true # Use TLS
  user: <EMAIL>
  pass: hfli jndb sgnb mgsl
  from: <EMAIL>
app:
  frontendUrl: 'http://localhost:3000'
  adminFrontendUrl: 'http://localhost:5174'
  backendUrl: 'http://localhost:4000'
  allowedOrigins:
    - 'http://localhost:3000'
    - 'http://localhost:5174'
    - 'http://localhost:4000'
  allowedMethods: 'GET,HEAD,OPTIONS,PUT,PATCH,POST,DELETE'
  domainName: 'localhost'
google:
  clientId: '30621720845-1gqapfn1pbijg2ffv5ps2kg0tuubv3lo.apps.googleusercontent.com'
  clientSecret: '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'
  callbackUrl: 'http://localhost:4000/auth/google/callback'
