'use client';

import AppHeader from './AppHeader';
import { usePathname } from 'next/navigation';

const hideHeaderRoutes = [
  '/request-domain-verification',
  '/verify-domain',
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
];

export default function HeaderVisibility() {
  const pathname = usePathname();
  if (hideHeaderRoutes.includes(pathname)) {
    return null;
  }
  return <AppHeader />;
}
