// schemas/job.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type JobDocument = Job & Document;

@Schema({ timestamps: true })
export class Job {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  company: string;

  @Prop({ required: true })
  location: string;

  @Prop({ required: true, default: false })
  remote_ok: boolean;

  @Prop({ required: true })
  salary_min: number;

  @Prop({ required: true })
  salary_max: number;

  @Prop({ required: true })
  description: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  employerId: string;

  @Prop({ required: true })
  domain: string;

  @Prop({ type: [String], default: [] })
  skills: string[];

  @Prop({
    required: true,
    type: Object,
    default: {},
  })
  criteria_weights: {
    technical_skills: number;
    soft_skills: number;
    experience: number;
    cultural_fit: number;
  };
}

export const JobSchema = SchemaFactory.createForClass(Job);
