'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormField,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useState, useEffect, useRef } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Wand2 } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { SkillsInput } from '@/components/candidate-profile/skills-input';
import { useAppDispatch, useAppSelector } from '@/store';
import type { CreateJobRequest } from '@/types/employee';
import {
  extractJobPost,
  clearJobPostExtractedData,
  generateJobDescription,
} from '@/store/slices/jobPostSlice';
import { Building, MapPin, DollarSign } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import toast from 'react-hot-toast';

const jobSchema = z
  .object({
    title: z.string().min(2, 'Title is required'),
    company: z.string().min(2, 'Company is required'),
    location: z.string().min(2, 'Location is required'),
    remote_ok: z.boolean(),
    salary_min: z.number().min(0, 'Minimum salary must be positive'),
    salary_max: z.number().min(0, 'Maximum salary must be positive'),
    description: z.string().min(10, 'Description is required'),
    domain: z.string().min(2, 'Domain is required'),
    skills: z.array(z.string()).optional(),
    criteria_weights: z.object({
      technical_skills: z.number().min(0, 'Required'),
      soft_skills: z.number().min(0, 'Required'),
      experience: z.number().min(0, 'Required'),
      cultural_fit: z.number().min(0, 'Required'),
    }),
  })
  .refine(data => data.salary_max >= data.salary_min, {
    message: 'Maximum salary must be greater than or equal to minimum salary',
    path: ['salary_max'],
  })
  .refine(
    data => {
      const totalWeight =
        data.criteria_weights.technical_skills +
        data.criteria_weights.soft_skills +
        data.criteria_weights.experience +
        data.criteria_weights.cultural_fit;
      return totalWeight === 100;
    },
    {
      message: 'Total criteria weights must equal 100',
      path: ['criteria_weights'],
    }
  );

interface JobFormProps {
  onSubmit: (data: CreateJobRequest) => Promise<void>;
  isLoading?: boolean;
}

export function JobForm({ onSubmit, isLoading = false }: JobFormProps) {
  const form = useForm<CreateJobRequest>({
    resolver: zodResolver(jobSchema),
    defaultValues: {
      title: '',
      company: '',
      location: '',
      remote_ok: false,
      salary_min: 0,
      salary_max: 0,
      description: '',
      domain: '',
      skills: [],
      criteria_weights: {
        technical_skills: 25,
        soft_skills: 25,
        experience: 25,
        cultural_fit: 25,
      },
    },
  });

  const [descLoading, setDescLoading] = useState(false);
  const [autoGenerating, setAutoGenerating] = useState(false);
  const dispatch = useAppDispatch();
  // Removed: jobPost is not part of employee state
  const jobPostExtract = useAppSelector(state => state.jobPost);
  const loadingToastId = useRef<string | null>(null);

  // Auto-generate description when title changes (debounced)
  useEffect(() => {
    const title = form.watch('title');
    if (!title || title.length < 3) return;

    const timeoutId = setTimeout(async () => {
      // Only auto-generate if description is empty
      const currentDescription = form.getValues('description');
      if (currentDescription.trim()) return;

      setAutoGenerating(true);
      let toastId: string | null = null;
      try {
        toastId = toast.loading('Generating job description with AI...');
        const data = await dispatch(generateJobDescription({ title })).unwrap();
        const generated = data?.data?.generated;
        if (generated) {
          const skillsArr = Array.isArray(generated.skills)
            ? generated.skills
            : (generated.skills as string)
                .split(',')
                .map((s: string) => s.trim())
                .filter(Boolean);
          const uniqueSkills = Array.from(new Set(skillsArr));
          form.setValue('description', generated.description || '');
          form.setValue('domain', generated.domain || '');
          form.setValue('skills', uniqueSkills);
          if (toastId)
            toast.success('AI job description generated!', { id: toastId });
        } else {
          if (toastId) toast.error('No data generated by AI.', { id: toastId });
        }
      } catch (err) {
        if (toastId)
          toast.error('Failed to auto-generate description.', { id: toastId });
        console.error('Failed to auto-generate description:', err);
      }
      setAutoGenerating(false);
    }, 2000); // Wait 2 seconds after user stops typing

    return () => clearTimeout(timeoutId);
  }, [dispatch, form, generateJobDescription]);

  useEffect(() => {
    if (jobPostExtract.extractedData) {
      const data = jobPostExtract.extractedData;
      if (data.title) form.setValue('title', data.title);
      if (data.description) form.setValue('description', data.description);
      if (data.domain) form.setValue('domain', data.domain);
      if (data.skills) {
        const uniqueSkills = Array.from(new Set(data.skills));
        form.setValue('skills', uniqueSkills);
      }
      dispatch(clearJobPostExtractedData());
    }
  }, [jobPostExtract.extractedData, form, dispatch]);

  useEffect(() => {
    if (jobPostExtract.extractLoading) {
      loadingToastId.current = toast.loading(
        'Extracting job details from PDF...'
      );
    } else if (!jobPostExtract.extractLoading && loadingToastId.current) {
      toast.dismiss(loadingToastId.current);
      loadingToastId.current = null;
      if (jobPostExtract.error) {
        toast.error('Failed to extract job details from PDF');
      } else if (jobPostExtract.extractedData) {
        toast.success('Job details extracted!');
      }
    }
  }, [
    jobPostExtract.extractLoading,
    jobPostExtract.error,
    jobPostExtract.extractedData,
  ]);

  const handleGenerateDescription = async () => {
    const title = form.getValues('title');
    if (!title) return;
    setDescLoading(true);
    let toastId: string | null = null;
    try {
      toastId = toast.loading('Generating job description with AI...');
      const data = await dispatch(generateJobDescription({ title })).unwrap();
      const generated = data?.data?.generated;
      if (generated) {
        const skillsArr = Array.isArray(generated.skills)
          ? generated.skills
          : (generated.skills as string)
              .split(',')
              .map((s: string) => s.trim())
              .filter(Boolean);
        const uniqueSkills = Array.from(new Set(skillsArr));
        form.setValue('description', generated.description || '');
        form.setValue('domain', generated.domain || '');
        form.setValue('skills', uniqueSkills);
        if (toastId)
          toast.success('AI job description generated!', { id: toastId });
      } else {
        if (toastId) toast.error('No data generated by AI.', { id: toastId });
      }
    } catch (err) {
      if (toastId)
        toast.error('Failed to generate job description.', { id: toastId });
      alert('Failed to generate ai');
    }
    setDescLoading(false);
  };

  const handlePdfChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      dispatch(extractJobPost(file)); // Trigger extraction immediately
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-5"
        autoComplete="off"
      >
        <Card className="shadow-2xl border-2 border-blue-100 rounded-3xl bg-white/90 backdrop-blur-md">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle>Post a Job</CardTitle>
            <div>
              <Label htmlFor="job-pdf-upload" className="sr-only">
                Upload PDF
              </Label>
              <Input
                id="job-pdf-upload"
                type="file"
                accept="application/pdf"
                onChange={handlePdfChange}
                disabled={jobPostExtract.extractLoading}
                className="hidden"
              />
              <Button
                asChild
                variant="outline"
                size="sm"
                className="ml-2"
                disabled={jobPostExtract.extractLoading}
              >
                <label htmlFor="job-pdf-upload" className="cursor-pointer">
                  Upload PDF
                </label>
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4 pt-0">
            {/* Row 1: Job Title, Company, Location */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter job title"
                        {...field}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="company"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          placeholder="Enter company name"
                          {...field}
                          className="w-full pl-10"
                        />
                        <Building className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          placeholder="Enter job location"
                          {...field}
                          className="w-full pl-10"
                        />
                        <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {/* Row 2: Remote, Min Salary, Max Salary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="remote_ok"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-2">
                    <FormLabel className="text-base">Remote</FormLabel>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="salary_min"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Min Salary</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="number"
                          min={0}
                          placeholder="0"
                          {...field}
                          onChange={e => field.onChange(Number(e.target.value))}
                          className="w-full pl-10"
                        />
                        <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="salary_max"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Max Salary</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="number"
                          min={0}
                          placeholder="0"
                          {...field}
                          onChange={e => field.onChange(Number(e.target.value))}
                          className="w-full pl-10"
                        />
                        <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {/* Divider */}
            <div className="border-t pt-4" />
            {/* Row 3: Criteria Weights (all in one row) */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <FormField
                control={form.control}
                name="criteria_weights.technical_skills"
                render={({ field }) => (
                  <FormItem>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <FormLabel>Technical</FormLabel>
                        </TooltipTrigger>
                        <TooltipContent>Technical skills weight</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <FormControl>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        {...field}
                        onChange={e => field.onChange(Number(e.target.value))}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="criteria_weights.soft_skills"
                render={({ field }) => (
                  <FormItem>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <FormLabel>Soft</FormLabel>
                        </TooltipTrigger>
                        <TooltipContent>Soft skills weight</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <FormControl>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        {...field}
                        onChange={e => field.onChange(Number(e.target.value))}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="criteria_weights.experience"
                render={({ field }) => (
                  <FormItem>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <FormLabel>Experience</FormLabel>
                        </TooltipTrigger>
                        <TooltipContent>Experience weight</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <FormControl>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        {...field}
                        onChange={e => field.onChange(Number(e.target.value))}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="criteria_weights.cultural_fit"
                render={({ field }) => (
                  <FormItem>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <FormLabel>Cultural</FormLabel>
                        </TooltipTrigger>
                        <TooltipContent>Cultural fit weight</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <FormControl>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        {...field}
                        onChange={e => field.onChange(Number(e.target.value))}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="text-xs text-muted-foreground text-right">
              Total:{' '}
              {(form.watch('criteria_weights.technical_skills') || 0) +
                (form.watch('criteria_weights.soft_skills') || 0) +
                (form.watch('criteria_weights.experience') || 0) +
                (form.watch('criteria_weights.cultural_fit') || 0)}
              /100
            </div>
            {/* Divider */}
            <div className="border-t pt-4" />
            {/* Description, Domain, Skills */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter job description"
                      {...field}
                      className="w-full"
                    />
                  </FormControl>
                  <div className="flex flex-col gap-2 mt-2">
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={handleGenerateDescription}
                      disabled={
                        descLoading ||
                        autoGenerating ||
                        !form.watch('title') ||
                        jobPostExtract.extractLoading
                      }
                      className="w-full flex items-center gap-2"
                    >
                      {descLoading ? (
                        <>
                          <LoadingSpinner size="sm" /> Generating...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4" /> Generate With AI
                        </>
                      )}
                    </Button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="domain"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Domain</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter domain"
                      {...field}
                      className="w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="skills"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Skills</FormLabel>
                  <SkillsInput
                    value={field.value || []}
                    onChange={skills => {
                      if (
                        JSON.stringify(skills) !== JSON.stringify(field.value)
                      ) {
                        form.setValue('skills', skills);
                      }
                    }}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl shadow-md py-3 text-base transition-colors"
              disabled={isLoading}
            >
              {isLoading ? 'Posting...' : 'Post Job'}
            </Button>
          </CardContent>
        </Card>
      </form>
    </Form>
  );
}
