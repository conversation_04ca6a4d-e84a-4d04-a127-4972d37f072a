'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { fetchRoles } from '@/store/slices/rolesSlice';
import { useAppDispatch, useAppSelector } from '@/store';
import type { Role } from '@/types/roles';
import React from 'react';
import Image from 'next/image';
import { User, Briefcase } from 'lucide-react';
import Link from 'next/link';
import Loader from '@/components/ui/Loader';

export default function ChooseRolePage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const {
    roles,
    loading: isLoading,
    error,
  } = useAppSelector(state => state.roles);
  const [selectedRoleId, setSelectedRoleId] = React.useState<string | null>(
    null
  );

  React.useEffect(() => {
    dispatch(fetchRoles());
  }, [dispatch]);

  const handleSelectRole = (roleId: string) => {
    setSelectedRoleId(roleId);
  };

  const handleContinue = () => {
    if (selectedRoleId) {
      localStorage.setItem('selectedRoleId', selectedRoleId);
      router.push('/auth/register');
    }
  };

  const roleLabels: Record<string, string> = {
    candidate: 'Candidate',
    employer: 'Employer',
    employee: 'Employer',
  };

  const roleIcons: Record<string, typeof User> = {
    candidate: User,
    employer: Briefcase,
    employee: Briefcase,
  };

  const getRoleLabel = (roleName: string): string => {
    const key = roleName.toLowerCase();
    return roleLabels[key] || 'Role';
  };

  const getRoleIcon = (roleName: string): typeof User => {
    const key = roleName.toLowerCase();
    return roleIcons[key] || User;
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Loader />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-red-500 text-lg">Error loading roles.</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Side */}
      <div className="hidden md:flex flex-col justify-center items-center w-1/2 bg-gradient-to-b from-[#1976F6] to-[#4F8DFD] rounded-tr-[40px] rounded-br-[40px] p-12 relative">
        <h1 className="text-white text-4xl font-extrabold mb-4 text-center drop-shadow-lg">
          Where Top Talents Meet Opportunities
        </h1>
        <p className="text-white text-lg text-center opacity-80 max-w-md">
          TalentLoop connects top candidates with leading employers using
          AI-powered matching. Discover opportunities, streamline hiring, and
          unlock your career potential—all in one platform.
        </p>
      </div>
      {/* Right Side */}
      <div className="flex-1 flex flex-col min-h-screen bg-white">
        {/* Top Bar */}
        <div className="flex items-center justify-between px-10 pt-8">
          <Image
            src="/assets/logo/TalentLoop.svg"
            alt="TalentLoop Logo"
            width={140}
            height={36}
          />
          <nav className="flex gap-8 text-[#1976F6] font-medium text-base">
            <Link href="/">Home</Link>
            <a href="#">About us</a>
            <a href="#">Pricing</a>
            <a href="#">Contact Us</a>
          </nav>
        </div>
        {/* Main Content */}
        <div className="flex flex-1 flex-col justify-center items-center py-8">
          <h2 className="text-3xl md:text-4xl font-bold text-[#222] mb-2 mt-8 md:mt-0">
            Choose Your path
          </h2>
          <p className="text-lg text-gray-500 mb-8">
            {"Select how you'd like to use TalentLoop"}
          </p>
          <div className="flex flex-col gap-6 w-full max-w-md">
            {roles.map((role: Role) => {
              const Icon: typeof User = getRoleIcon(role.name);
              return (
                <label
                  key={role._id}
                  className={`flex items-center gap-6 border-2 rounded-2xl px-8 py-6 cursor-pointer transition-all ${selectedRoleId === role._id ? 'border-[#1976F6] bg-[#F5F9FF]' : 'border-[#1976F6]/30 bg-white hover:bg-[#F5F9FF]'}`}
                  onClick={() => handleSelectRole(role._id)}
                >
                  <span className="flex items-center justify-center w-16 h-16 rounded-full border-2 border-[#1976F6]/30">
                    <Icon className="w-10 h-10 text-[#1976F6]" />
                  </span>
                  <div className="flex-1">
                    <div className="text-xl font-bold text-[#1976F6] mb-1">
                      {"I'm a"} {getRoleLabel(role.name)}
                    </div>
                    <div className="text-sm text-gray-500">
                      {role.description}
                    </div>
                  </div>
                  <span
                    className={`w-6 h-6 flex items-center justify-center rounded-full border-2 ${selectedRoleId === role._id ? 'border-[#1976F6] bg-[#1976F6]' : 'border-[#1976F6]/30 bg-white'}`}
                  >
                    {selectedRoleId === role._id && (
                      <span className="w-3 h-3 rounded-full bg-white block" />
                    )}
                  </span>
                </label>
              );
            })}
          </div>
          <Button
            className="mt-10 w-full max-w-md"
            onClick={handleContinue}
            disabled={!selectedRoleId}
          >
            Continue
          </Button>
        </div>
      </div>
    </div>
  );
}
