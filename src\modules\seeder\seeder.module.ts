import { Modu<PERSON> } from '@nestjs/common';
import { RoleSeeder } from './role.seeder';
import { RoleModule } from '../role/role.module';
import { DatabaseModule } from 'src/database/database.module';
import { ConfigModule } from '@nestjs/config';
import { UserSeeder } from './user.seeder';
import { MongooseModule } from '@nestjs/mongoose';
import {
  AdminProfile,
  AdminProfileSchema,
} from '../adminProfile/schemas/admin-profile.schema';

@Module({
  imports: [
    ConfigModule,
    RoleModule,
    DatabaseModule,
    MongooseModule.forFeature([
      { name: AdminProfile.name, schema: AdminProfileSchema },
    ]),
  ],
  providers: [RoleSeeder, UserSeeder],
  exports: [RoleSeeder, UserSeeder],
})
export class SeederModule {}
