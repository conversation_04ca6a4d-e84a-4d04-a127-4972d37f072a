import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type CandidateProfileDocument = CandidateProfile & Document;

@Schema({ timestamps: true })
export class CandidateProfile {
  // Reference to the user collection
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  // Personal Info
  @Prop({ required: true })
  fullName: string;

  @Prop({ required: true, unique: true })
  email: string;

  @Prop()
  phoneNumber: string;

  @Prop()
  currentJobTitle: string;

  // Location
  @Prop()
  permanentAddress: string;

  @Prop()
  country?: string;

  @Prop()
  city?: string;

  @Prop()
  postalCode?: string;

  // Bio
  @Prop()
  bio: string;

  // Education (array of embedded documents)
  @Prop({
    type: [
      {
        institution: { type: String, required: true },
        degree: { type: String },
        fieldOfStudy: { type: String },
        startYear: { type: String },
        endYear: { type: String },
        grade: { type: String },
        country: { type: String },
      },
    ],
    default: [],
  })
  educations: {
    institution: string;
    degree?: string;
    fieldOfStudy?: string;
    startYear?: string;
    endYear?: string;
    grade?: string;
    country?: string;
  }[];

  // Work Experience (array of embedded documents)
  @Prop({
    type: [
      {
        company: { type: String, required: true },
        title: { type: String },
        location: { type: String },
        from_date: { type: Date },
        to_date: { type: Date },
        currentlyWorking: { type: Boolean, default: false },
        description: { type: String },
      },
    ],
    default: [],
  })
  workExperience: {
    company: string;
    title?: string;
    location?: string;
    from_date?: Date;
    to_date?: Date;
    currentlyWorking?: boolean;
    description?: string;
  }[];

  // Skills and Technologies
  @Prop({ type: [String], default: [] })
  skills: string[];

  // Optional profile image URL
  @Prop()
  image?: string;

  // Resume
  @Prop()
  resume: string;

  // Static Profile Scores
  @Prop({
    type: {
      technical_skills: { type: Number, default: 0 },
      soft_skills: { type: Number, default: 0 },
      experience: { type: Number, default: 0 },
      cultural_fit: { type: Number, default: 0 },
      total_static_score: { type: Number, default: 0 },
    },
    default: {
      technical_skills: 0,
      soft_skills: 0,
      experience: 0,
      cultural_fit: 0,
      total_static_score: 0,
    },
  })
  static_profile: {
    technical_skills: number;
    soft_skills: number;
    experience: number;
    cultural_fit: number;
    total_static_score: number;
  };

  // Certifications
  @Prop({
    type: [
      {
        name: { type: String },
        authority: { type: String },
        licenseNumber: { type: String },
        year: { type: String },
      },
    ],
    default: [],
  })
  certifications: {
    name: string;
    authority?: string;
    licenseNumber?: string;
    year?: string;
  }[];
}

export const CandidateProfileSchema =
  SchemaFactory.createForClass(CandidateProfile);
