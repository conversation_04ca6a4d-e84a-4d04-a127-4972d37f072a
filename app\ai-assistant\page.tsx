import React from 'react';

const features = [
  {
    title: 'Review My CV',
    description: 'Get Feedback on your resume',
  },
  {
    title: 'Find Job Matches',
    description: 'Discover relevant opportunities',
  },
  {
    title: 'Interview Preparation',
    description: 'Practice Common Questions',
  },
  {
    title: 'Career Advice',
    description: 'Get Personalized Guidance',
  },
];

export default function AiAssistantPage() {
  return (
    <div className="min-h-screen flex flex-col justify-between bg-gradient-to-b from-blue-100 to-white">
      <div className="flex-1 flex flex-col items-center justify-center px-2 py-10">
        {/* Main Icon */}
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 flex items-center justify-center rounded-full bg-blue-200 mb-6">
            <span className="text-3xl">✨</span>
          </div>
          {/* Heading */}
          <h1 className="text-3xl md:text-4xl font-bold text-center mb-4">
            How can we assist you today?
          </h1>
          {/* Subtitle */}
          <p className="text-gray-500 text-center max-w-xl mb-8">
            Get expert guidance powered by AI agents specializing in Sales,
            Marketing, and Negotiation. Choose the agent that suits your needs
            and start your conversation with ease.
          </p>
        </div>
        {/* Message Input Bar */}
        <div className="w-full max-w-2xl flex items-center bg-white rounded-full shadow px-4 py-2 mb-8">
          <input
            type="text"
            placeholder="Send a message"
            className="flex-1 bg-transparent outline-none border-none text-gray-700 px-2 py-2 text-sm"
          />
          <button
            className="mx-2 text-gray-400 hover:text-blue-600 transition-colors"
            title="Attach file"
          >
            <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
              <path
                stroke="currentColor"
                strokeWidth="2"
                d="M16.5 12.5v3a4.5 4.5 0 01-9 0v-7a4.5 4.5 0 019 0v7a2.5 2.5 0 01-5 0v-7"
              />
            </svg>
          </button>
          <button
            className="mx-2 text-gray-400 hover:text-blue-600 transition-colors"
            title="Record voice"
          >
            <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
              <path
                stroke="currentColor"
                strokeWidth="2"
                d="M12 18v2m0 0h3m-3 0H9m3-2a6 6 0 006-6V9a6 6 0 10-12 0v3a6 6 0 006 6z"
              />
            </svg>
          </button>
          <button className="ml-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-full px-6 py-2 shadow hover:from-blue-600 hover:to-blue-700 transition-colors">
            Submit
          </button>
        </div>
        {/* Feature Cards */}
        <div className="w-full max-w-4xl grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mt-2">
          {features.map(f => (
            <div
              key={f.title}
              className="bg-white rounded-2xl shadow p-6 flex flex-col items-start hover:shadow-lg transition-shadow cursor-pointer group"
            >
              <div className="flex items-center mb-2 w-full justify-between">
                <span className="text-lg font-semibold text-gray-800 group-hover:text-blue-700 transition-colors">
                  {f.title}
                </span>
                <span className="ml-2 text-gray-400 group-hover:text-blue-600 transition-colors">
                  <svg width="22" height="22" fill="none" viewBox="0 0 24 24">
                    <path
                      stroke="currentColor"
                      strokeWidth="2"
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </span>
              </div>
              <span className="text-gray-500 text-sm">{f.description}</span>
            </div>
          ))}
        </div>
      </div>
      {/* Footer */}
      <footer className="w-full text-center text-gray-400 text-sm pb-6">
        Powered by TalentLoop
      </footer>
    </div>
  );
}
