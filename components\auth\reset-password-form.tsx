import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState } from 'react';
import { Eye, EyeOff, Lock } from 'lucide-react';

interface ResetPasswordFormProps {
  onSubmit: (data: { newPassword: string; confirmPassword: string }) => void;
  isLoading?: boolean;
  message?: string;
  error?: string;
}

export function ResetPasswordForm({
  onSubmit,
  isLoading,
  message,
  error,
}: ResetPasswordFormProps) {
  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm<{ newPassword: string; confirmPassword: string }>();
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="password" className="text-sm font-medium">
          New Password
        </Label>
        <div className="relative">
          <Lock className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
          <Input
            id="password"
            type={showNewPassword ? 'text' : 'password'}
            placeholder="Enter new password"
            className={
              'pl-12 pr-12 h-12 rounded-lg text-base shadow-sm' +
              (errors.newPassword
                ? ' border-red-500 focus-visible:ring-red-500'
                : '')
            }
            {...register('newPassword', {
              required: 'Password is required',
              minLength: {
                value: 8,
                message: 'Password must be at least 8 characters',
              },
            })}
          />
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 p-1"
            tabIndex={-1}
            onClick={() => setShowNewPassword(v => !v)}
          >
            {showNewPassword ? (
              <EyeOff className="h-5 w-5 text-slate-400" />
            ) : (
              <Eye className="h-5 w-5 text-slate-400" />
            )}
          </button>
        </div>
        {errors.newPassword && (
          <p className="text-xs text-red-500">{errors.newPassword.message}</p>
        )}
      </div>
      <div className="space-y-2">
        <Label htmlFor="confirmPassword" className="text-sm font-medium">
          Confirm Password
        </Label>
        <div className="relative">
          <Lock className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
          <Input
            id="confirmPassword"
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder="Confirm new password"
            className={
              'pl-12 pr-12 h-12 rounded-lg text-base shadow-sm' +
              (errors.confirmPassword
                ? ' border-red-500 focus-visible:ring-red-500'
                : '')
            }
            {...register('confirmPassword', {
              required: 'Please confirm your password',
              validate: value =>
                value === getValues('newPassword') || 'Passwords do not match',
            })}
          />
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 p-1"
            tabIndex={-1}
            onClick={() => setShowConfirmPassword(v => !v)}
          >
            {showConfirmPassword ? (
              <EyeOff className="h-5 w-5 text-slate-400" />
            ) : (
              <Eye className="h-5 w-5 text-slate-400" />
            )}
          </button>
        </div>
        {errors.confirmPassword && (
          <p className="text-xs text-red-500">
            {errors.confirmPassword.message}
          </p>
        )}
      </div>
      <Button
        type="submit"
        disabled={isLoading}
        className="w-full h-12 rounded-full bg-gradient-to-r from-[#1976F6] to-[#4F8DFD] text-lg font-bold border-0 shadow-md hover:from-[#0858F8] hover:to-[#1976F6]"
      >
        {isLoading ? 'Resetting...' : 'Reset Password'}
      </Button>
      {message && <p className="text-green-600 text-sm mt-2">{message}</p>}
      {error && <p className="text-red-600 text-sm mt-2">{error}</p>}
    </form>
  );
}
