import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import * as express from 'express';
import * as path from 'path';
import cookieParser from 'cookie-parser';
import { ValidationPipe } from '@nestjs/common';
import helmet from 'helmet';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Use cookie-parser middleware
  app.use(cookieParser());
  app.use(helmet());
  // Enable CORS
  const allowedOrigins =
    configService.get<string[]>('app.allowedOrigins') || [];
  const allowedMethods =
    configService.get<string>('app.allowedMethods') ||
    'GET,HEAD,PUT,PATCH,POST,DELETE';

  app.enableCors({
    origin: (origin, callback) => {
      console.log('CORS request from:', origin);
      if (!origin) return callback(null, true);
      if (allowedOrigins.includes(origin as string)) {
        return callback(null, true);
      }
      return callback(new Error('Not allowed by CORS'), false);
    },
    methods: allowedMethods,
    credentials: true,
  });

  // Get port from config
  const port = configService.get<number>('app.port', 4000);
  app.useGlobalPipes(new ValidationPipe({ transform: true }));
  // Set up static file serving
  app.use(
    '/templates/images',
    express.static(join(__dirname, '..', 'templates/images')),
  );
  // Set up static file serving with CORS headers for /uploads
  app.use(
    '/uploads',
    (req, res, next) => {
      const origin = req.headers.origin;
      if (allowedOrigins.includes(origin as string)) {
        res.header('Access-Control-Allow-Origin', origin);
      }
      res.header('Access-Control-Allow-Methods', allowedMethods);
      res.header(
        'Access-Control-Allow-Headers',
        'Origin, X-Requested-With, Content-Type, Accept',
      );
      res.header('Cross-Origin-Resource-Policy', 'cross-origin');
      if (req.method === 'OPTIONS') {
        return res.sendStatus(204);
      }
      next();
    },
    express.static(join(__dirname, '..', 'uploads')),
  );

  const config = new DocumentBuilder()
    .setTitle('TalentLoop')
    .setDescription('The TalentLoop API description')
    .setVersion('1.0')
    .addTag('TalentLoop')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: { defaultModelsExpandDepth: -1 },
  });

  await app.listen(port);
  console.log(`Application is running on: http://localhost:${port}`);
}

void bootstrap();
function join(__dirname: string, ...paths: string[]): string {
  return path.join(__dirname, ...paths);
}
