import { Modu<PERSON> } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtStrategy } from './jwt.strategy';
import { UserModule } from '../user/user.module';
import { MailerModule } from '../mailer/mailer.module';
import { RoleService } from '../role/role.service';
import { RoleModule } from '../role/role.module';
import { CandidateProfileModule } from '../candidate/candidate.module';
import { EmployeeModule } from '../employee/employee.module';

@Module({
  imports: [
    MailerModule,
    UserModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('jwt.secret'),
        signOptions: { expiresIn: configService.get<string>('jwt.expiresIn') },
      }),
      inject: [ConfigService],
    }),
    RoleModule,
    CandidateProfileModule,
    EmployeeModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy, RoleService],
})
export class AuthModule {}
