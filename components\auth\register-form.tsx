'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Eye, EyeOff, Mail, Lock, User, Linkedin } from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { CredentialResponse, GoogleLogin } from '@react-oauth/google';
import { getGoogleResponse } from '@/store/slices/authSlice';
import { useAppDispatch } from '@/store';
import toast from 'react-hot-toast';
import { AuthResponse } from '@/types/auth';
import { useRouter } from 'next/navigation';

const registerSchema = z
  .object({
    firstName: z.string().min(2, 'First name must be at least 2 characters'),
    lastName: z.string().min(2, 'Last name must be at least 2 characters'),
    email: z.string().email('Please enter a valid email address'),
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one uppercase letter, one lowercase letter, and one number'
      ),
    confirmPassword: z.string(),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type RegisterFormData = z.infer<typeof registerSchema>;

interface RegisterFormProps {
  onSubmit: (
    data: Omit<RegisterFormData, 'confirmPassword' | 'acceptTerms'>
  ) => Promise<void>;
  onLinkedInSignUp: () => Promise<void>;
  isLoading?: boolean;
}

export function RegisterForm({
  onSubmit,
  onLinkedInSignUp,
  isLoading = false,
}: RegisterFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [socialLoading, setSocialLoading] = useState<
    'google' | 'linkedin' | null
  >(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });
  const dispatch = useAppDispatch();
  const router = useRouter();

  const handleSuccess = async (credentialResponse: CredentialResponse) => {
    try {
      const { credential } = credentialResponse;
      if (!credential) {
        toast.error('Google credential missing.');
        return;
      }

      console.log('Google Sign Up Credential:', credential);

      const roleId = localStorage.getItem('selectedRoleId') || '';

      const resultAction = await dispatch(
        getGoogleResponse({ role: roleId, idToken: credential })
      );

      if (getGoogleResponse.fulfilled.match(resultAction)) {
        const result = resultAction.payload as AuthResponse;

        console.log('Google Sign Up success:', result);

        toast.success('Registration successful!');

        // Store token
        if (result?.data?.access_token) {
          localStorage.setItem('authToken', result.data.access_token);
        }

        // Route handling based on user role
        const user = result.data.user;
        if (user?.roleName === 'employee' && user?.domainVerified === false) {
          setTimeout(() => {
            router.replace('/verify-domain');
          }, 1500);
          return;
        }

        setTimeout(() => {
          router.replace('/dashboard');
        }, 1500);
      } else {
        const error = resultAction.payload as { message: string };
        console.error('Google Sign Up error:', error);
        toast.error(error.message || 'Registration failed.');
      }
    } catch (error) {
      console.error('Google Sign Up error:', error);
      toast.error('Google Sign Up failed.');
    }
  };

  const handleLinkedInSignUp = async () => {
    setSocialLoading('linkedin');
    try {
      await onLinkedInSignUp();
    } finally {
      setSocialLoading(null);
    }
  };

  const handleFormSubmit = async (data: RegisterFormData) => {
    await onSubmit(data);
  };

  const isFormLoading = isLoading || isSubmitting;

  return (
    <div className="w-full max-w-md p-8 rounded-2xl shadow-2xl border-0 bg-white">
      <div className="text-xs font-semibold text-gray-500 tracking-widest mb-2 uppercase text-left">
        WELCOME
      </div>
      <div className="text-3xl font-extrabold text-gray-900 mb-2 text-left">
        Create your Account
      </div>
      <div className="text-lg text-gray-500 mb-6 text-left">
        Join us today and get started
      </div>
      {/* Registration Form */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-sm font-medium">
              First Name
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
              <Input
                id="firstName"
                type="text"
                placeholder="John"
                className={cn(
                  'pl-12 h-12 rounded-lg text-base shadow-sm',
                  errors.firstName &&
                    'border-red-500 focus-visible:ring-red-500'
                )}
                {...register('firstName')}
              />
            </div>
            {errors.firstName && (
              <p className="text-xs text-red-500">{errors.firstName.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-sm font-medium">
              Last Name
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
              <Input
                id="lastName"
                type="text"
                placeholder="Doe"
                className={cn(
                  'pl-12 h-12 rounded-lg text-base shadow-sm',
                  errors.lastName && 'border-red-500 focus-visible:ring-red-500'
                )}
                {...register('lastName')}
              />
            </div>
            {errors.lastName && (
              <p className="text-xs text-red-500">{errors.lastName.message}</p>
            )}
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium">
            Email
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              className={cn(
                'pl-12 h-12 rounded-lg text-base shadow-sm',
                errors.email && 'border-red-500 focus-visible:ring-red-500'
              )}
              {...register('email')}
            />
          </div>
          {errors.email && (
            <p className="text-xs text-red-500">{errors.email.message}</p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="password" className="text-sm font-medium">
            Password
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
            <Input
              id="password"
              type={showPassword ? 'text' : 'password'}
              placeholder="Enter your password"
              className={cn(
                'pl-12 pr-12 h-12 rounded-lg text-base shadow-sm',
                errors.password && 'border-red-500 focus-visible:ring-red-500'
              )}
              {...register('password')}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2"
              onClick={() => setShowPassword(!showPassword)}
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-slate-400" />
              ) : (
                <Eye className="h-5 w-5 text-slate-400" />
              )}
            </Button>
          </div>
          {errors.password && (
            <p className="text-xs text-red-500">{errors.password.message}</p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-sm font-medium">
            Confirm Password
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="Confirm your password"
              className={cn(
                'pl-12 pr-12 h-12 rounded-lg text-base shadow-sm',
                errors.confirmPassword &&
                  'border-red-500 focus-visible:ring-red-500'
              )}
              {...register('confirmPassword')}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              tabIndex={-1}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-5 w-5 text-slate-400" />
              ) : (
                <Eye className="h-5 w-5 text-slate-400" />
              )}
            </Button>
          </div>
          {errors.confirmPassword && (
            <p className="text-xs text-red-500">
              {errors.confirmPassword.message}
            </p>
          )}
        </div>
        <Button
          type="submit"
          className="w-full h-12 rounded-full bg-gradient-to-r from-[#1976F6] to-[#4F8DFD] text-lg font-bold border-0 shadow-md hover:from-[#0858F8] hover:to-[#1976F6]"
          disabled={isFormLoading}
        >
          {isFormLoading ? (
            <LoadingSpinner size="sm" className="mr-2" />
          ) : (
            'Sign Up'
          )}
        </Button>
      </form>
      {/* Divider */}
      <div className="flex items-center my-6">
        <div className="flex-1 h-px bg-gray-200" />
        <span className="mx-4 text-gray-400 font-medium">Or</span>
        <div className="flex-1 h-px bg-gray-200" />
      </div>
      {/* Social Buttons */}
      <div className="space-y-3 mb-4">
        <GoogleLogin
          onSuccess={handleSuccess}
          onError={() => console.error('Login Failed')}
        />
        <Button
          type="button"
          className="w-full h-12 bg-white text-[#0D47A1] border border-gray-300 rounded-full flex items-center justify-center gap-2 shadow-sm hover:bg-gray-100 transition-colors"
          onClick={handleLinkedInSignUp}
          disabled={!!socialLoading}
        >
          {socialLoading === 'linkedin' ? (
            <LoadingSpinner size="sm" className="mr-2" />
          ) : (
            <Linkedin className="h-5 w-5 text-blue-600" />
          )}
          Sign up with LinkedIn
        </Button>
      </div>
      {/* Log in link */}
      <div className="text-center text-sm text-gray-600 mt-2">
        Already have an account?{' '}
        <Link
          href="/auth/login"
          className="text-[#1976F6] font-semibold hover:underline"
        >
          Log in
        </Link>
      </div>
    </div>
  );
}
