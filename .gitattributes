# Ignore compiled TypeScript and build output
/dist/** linguist-vendored
/node_modules/** linguist-vendored

# Ensure GitHub shows correct primary language
*.ts linguist-language=TypeScript
*.tsx linguist-language=TypeScript
*.js linguist-language=JavaScript
*.jsx linguist-language=JavaScript

# Treat static assets as vendored
/public/** linguist-vendored

# Normalize text files across platforms
*.ts text eol=lf
*.tsx text eol=lf
*.js text eol=lf
*.json text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
*.md text eol=lf
*.env text eol=lf

# Mark shell scripts as text
*.sh text eol=lf

# Optional: if you use Prisma
/prisma/generated/** linguist-vendored

# Optional: if using Swagger-generated files
/src/swagger/** linguist-vendored
