import axios from 'axios';
import Router from 'next/router';

// Create axios instance
const api = axios.create({
  // baseURL: process.env.NEXT_PUBLIC_API_URL, // Uncomment and set if you have a base API URL
  withCredentials: true, // Send cookies if needed
});

// Add a response interceptor
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response && error.response.status === 401) {
      // Clear auth state if you have a logout action
      if (typeof window !== 'undefined') {
        // Optionally clear localStorage/sessionStorage
        localStorage.removeItem('persist:root');
        // Redirect to login
        Router.replace('/auth/login');
      }
    }
    return Promise.reject(error);
  }
);

export default api;
