import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  UserActivity,
  UserActivityDocument,
} from './schemas/user-activity.schema';
import { UserActivityDto } from './dto/create-user-activity.dto';

@Injectable()
export class UserActivityService {
  constructor(
    @InjectModel(UserActivity.name)
    private readonly userActivityModel: Model<UserActivityDocument>,
  ) {}

  async userActivity(
    activityDto: UserActivityDto,
    userId: string,
    headers: {
      browser: string;
      source: string;
      endpoint: string;
      origin: string;
      osName: string;
      osVersion: string;
      agentVersion: string;
      ipAddress: string;
    },
  ): Promise<UserActivity> {
    try {
      const activity = new this.userActivityModel({
        userId,
        eventName: activityDto.eventName,
        screenName: activityDto.screenName,
        slug: activityDto.slug,
        description: activityDto.description,
        request: activityDto.request || {},
        ...headers,
      });

      return await activity.save();
    } catch (error) {
      console.error('Failed to log user activity', error);
      throw error;
    }
  }

  async getActivitiesByUserId(userId: string): Promise<UserActivity[]> {
    return this.userActivityModel
      .find({ userId })
      .sort({ createdAt: -1 }) // optional: most recent first
      .exec();
  }
}
