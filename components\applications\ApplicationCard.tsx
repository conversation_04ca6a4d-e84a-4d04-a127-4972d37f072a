import React from 'react';
import ProgressBar from './ProgressBar';
import InterviewerChip from './InterviewerChip';

interface Interviewer {
  name: string;
  role: string;
}

interface Application {
  id: number;
  title: string;
  status: string;
  company: string;
  location: string;
  appliedDate: string;
  salary: string;
  progress: number;
  progressLabel: string;
  interviewers: Interviewer[];
  viewJobUrl: string;
  viewDetailsUrl: string;
}

const ApplicationCard: React.FC<{ app: Application }> = ({ app }) => {
  const statusColor =
    app.status === 'Interview'
      ? 'bg-yellow-500'
      : app.status === 'Offer'
        ? 'bg-green-600'
        : 'bg-gray-400';
  const progressColor =
    app.status === 'Interview' ? 'bg-yellow-500' : 'bg-green-600';
  return (
    <div className="bg-white rounded-2xl shadow p-6 mb-6 transition-transform hover:scale-[1.01] hover:shadow-lg">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <div className="flex items-center mb-2">
            <h3 className="text-lg font-semibold mr-3">{app.title}</h3>
            <span
              className={`text-xs text-white px-2 py-1 rounded ${statusColor}`}
            >
              {app.status}
            </span>
          </div>
          <div className="flex items-center text-gray-500 text-sm mb-1">
            <span className="mr-4">🏢 {app.company}</span>
            <span className="mr-4">📍 {app.location}</span>
            <span>🗓️ Applied {app.appliedDate}</span>
          </div>
          <div className="text-blue-700 font-bold text-md mb-1">
            {app.salary}
          </div>
          <div className="text-xs text-gray-600 mb-1">
            Progress: {app.progressLabel}
          </div>
          <ProgressBar percent={app.progress} color={progressColor} />
          <div className="text-xs text-gray-600 mt-2 mb-1">Interviewers:</div>
          <div className="flex flex-wrap">
            {app.interviewers.map((i, idx) => (
              <InterviewerChip key={idx} name={i.name} role={i.role} />
            ))}
          </div>
        </div>
        <div className="flex flex-col items-end mt-4 md:mt-0">
          <a
            href={app.viewJobUrl}
            className="border border-blue-600 text-blue-600 rounded-lg px-4 py-2 mb-2 hover:bg-blue-50 text-sm transition-colors"
          >
            View Job
          </a>
          <a
            href={app.viewDetailsUrl}
            className="bg-blue-600 text-white rounded-lg px-6 py-2 hover:bg-blue-700 text-sm transition-colors"
          >
            View Details
          </a>
        </div>
      </div>
    </div>
  );
};

export default ApplicationCard;
