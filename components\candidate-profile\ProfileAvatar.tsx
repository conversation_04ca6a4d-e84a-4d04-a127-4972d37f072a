import React from 'react';
import { Button } from '@/components/ui/button';

interface ProfileAvatarProps {
  image: string | null;
  editing: boolean;
  onChange: (file: File, croppedDataUrl?: string) => void;
  onRemove: () => void;
}

const ProfileAvatar: React.FC<ProfileAvatarProps> = ({
  image,
  editing,
  onChange,
  onRemove,
}) => {
  return (
    <div className="flex flex-col items-center">
      <div className="relative w-24 h-24 mb-2">
        {image ? (
          <img
            src={image}
            alt="Profile"
            className="w-24 h-24 rounded-full object-cover border-2 border-blue-200"
          />
        ) : (
          <div className="w-24 h-24 rounded-full bg-blue-100 flex items-center justify-center text-4xl text-blue-400">
            ?
          </div>
        )}
      </div>
      {editing ? (
        <div className="flex gap-2">
          <input
            type="file"
            accept="image/*"
            id="avatar-upload"
            style={{ display: 'none' }}
            onChange={e => {
              if (e.target.files && e.target.files[0]) {
                onChange(e.target.files[0]);
              }
            }}
          />
          <label htmlFor="avatar-upload">
            <Button type="button" variant="outline" size="sm">
              Edit
            </Button>
          </label>
          <Button
            type="button"
            variant="destructive"
            size="sm"
            onClick={onRemove}
          >
            Remove
          </Button>
        </div>
      ) : null}
    </div>
  );
};

export default ProfileAvatar;
