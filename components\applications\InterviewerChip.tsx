import React from 'react';

interface InterviewerChipProps {
  name: string;
  role: string;
}

function getInitials(name: string) {
  return name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase();
}

const InterviewerChip: React.FC<InterviewerChipProps> = ({ name, role }) => (
  <div className="flex items-center bg-gray-100 rounded-full px-3 py-1 text-xs mr-2 mb-2">
    <span className="w-6 h-6 flex items-center justify-center bg-blue-200 text-blue-700 rounded-full mr-2 font-bold">
      {getInitials(name)}
    </span>
    <span className="font-medium">{name}</span>
    <span className="ml-1 text-gray-500">- {role}</span>
  </div>
);

export default InterviewerChip;
