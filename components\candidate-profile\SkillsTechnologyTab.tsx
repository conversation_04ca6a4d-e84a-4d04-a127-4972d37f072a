import React from 'react';
import SkillsInput from './skills-input';

interface SkillsTechnologyTabProps {
  skills: string[];
  editing: boolean;
  handleArrayAdd: (field: 'skills', emptyObj: Record<string, unknown>) => void;
  handleArrayRemove: (field: 'skills', idx: number) => void;
  handleArrayChange: (
    field: 'skills',
    idx: number,
    subfield: string,
    value: string | number | boolean
  ) => void;
  onChange: (skills: string[]) => void;
}

const SkillsTechnologyTab: React.FC<SkillsTechnologyTabProps> = ({
  skills,
  onChange,
}) => {
  return (
    <div>
      <SkillsInput value={skills} onChange={onChange} />
    </div>
  );
};

export default SkillsTechnologyTab;
