import { logUserActivity } from '../store/slices/userActivitySlice';
import { useAppDispatch } from '../store';
import { usePathname } from 'next/navigation';
import { useRef, useCallback } from 'react';
import { useSelector } from 'react-redux';

export const useUserActivity = () => {
  const dispatch = useAppDispatch();
  const pathname = usePathname();
  const lastActivityRef = useRef<{
    eventName: string;
    timestamp: number;
  } | null>(null);
  // Avoid returning a new object reference on each render
const user = useSelector((state: any) => state.auth.user);

  const trackActivity = useCallback(
    async (
      eventName: string,
      description: string,
      additionalData: Record<string, unknown> = {}
    ) => {
      // Only track if user is authenticated
      if (!user) {
        return;
      }

      const now = Date.now();
      const lastActivity = lastActivityRef.current;

      // Prevent duplicate activities within 1 second
      if (
        lastActivity &&
        lastActivity.eventName === eventName &&
        now - lastActivity.timestamp < 1000
      ) {
        return;
      }

      lastActivityRef.current = { eventName, timestamp: now };

      try {
        await dispatch(
          logUserActivity({
            eventName,
            screenName: pathname || '/',
            slug: pathname || '/',
            description,
            request: {
              path: pathname || '/',
              ...additionalData,
            },
          })
        );
      } catch (error) {
        // Silently fail to not interrupt user experience
        console.warn('Failed to log user activity:', error);
      }
    },
    [dispatch, pathname, user]
  );

  // Predefined activity tracking functions
  const trackPageView = useCallback(
    (pageName: string) => {
      trackActivity('PAGE_VIEW', `User viewed ${pageName} page`);
    },
    [trackActivity]
  );

  const trackButtonClick = useCallback(
    (buttonName: string, context?: string) => {
      trackActivity('BUTTON_CLICK', `User clicked ${buttonName}`, { context });
    },
    [trackActivity]
  );

  const trackFormSubmission = useCallback(
    (formName: string, success: boolean) => {
      trackActivity('FORM_SUBMISSION', `User submitted ${formName} form`, {
        success,
        formName,
      });
    },
    [trackActivity]
  );

  const trackNavigation = useCallback(
    (from: string, to: string) => {
      trackActivity('NAVIGATION', `User navigated from ${from} to ${to}`, {
        from,
        to,
      });
    },
    [trackActivity]
  );

  const trackSearch = useCallback(
    (searchTerm: string, resultsCount?: number) => {
      trackActivity('SEARCH', `User searched for: ${searchTerm}`, {
        searchTerm,
        resultsCount,
      });
    },
    [trackActivity]
  );

  const trackJobApplication = useCallback(
    (jobId: string, jobTitle: string) => {
      trackActivity('JOB_APPLICATION', `User applied to job: ${jobTitle}`, {
        jobId,
        jobTitle,
      });
    },
    [trackActivity]
  );

  const trackProfileUpdate = useCallback(
    (section: string) => {
      trackActivity(
        'PROFILE_UPDATE',
        `User updated profile section: ${section}`,
        {
          section,
        }
      );
    },
    [trackActivity]
  );

  return {
    trackActivity,
    trackPageView,
    trackButtonClick,
    trackFormSubmission,
    trackNavigation,
    trackSearch,
    trackJobApplication,
    trackProfileUpdate,
  };
};
