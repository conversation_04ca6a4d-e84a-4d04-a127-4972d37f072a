import React from 'react';

interface Insight {
  company: string;
  role: string;
  location: string;
  match: string;
  tags: string[];
}

export default function JobMatches({ aiInsights }: { aiInsights: Insight[] }) {
  return (
    <div className="bg-white rounded-2xl shadow p-6">
      <h2 className="font-semibold text-lg mb-4">Job Matches</h2>
      <div className="flex flex-col gap-4">
        {aiInsights.map((insight, i) => (
          <div
            key={i}
            className="bg-[#F5F8FF] rounded-xl p-4 flex flex-col gap-2"
          >
            <div className="font-medium">
              We matched you with{' '}
              <span className="text-[#377DFF]">{insight.company}</span> for{' '}
              <span className="text-[#377DFF]">{insight.role}</span>
            </div>
            <div className="text-sm text-gray-500">
              Tech Companies • {insight.location} •{' '}
              <span className="text-green-600 font-semibold">
                {insight.match} match
              </span>
            </div>
            <div className="flex gap-2 mt-1">
              {insight.tags.map((tag, j) => (
                <span
                  key={j}
                  className="bg-white border border-[#E0E7FF] text-xs rounded-full px-3 py-1 text-[#377DFF]"
                >
                  {tag}
                </span>
              ))}
            </div>
            <button className="text-[#377DFF] text-sm font-medium mt-2 self-start hover:underline transition">
              Improve Match &rarr;
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
