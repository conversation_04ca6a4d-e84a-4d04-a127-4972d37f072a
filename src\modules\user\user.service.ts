import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { User } from './schemas/user.schema';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { EncryptionUtil } from '../../utils/encryption.util';
import * as bcrypt from 'bcrypt';
import { ERROR } from '../../utils/error-code';

@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name)
    private userModel: Model<User>,
    private configService: ConfigService,
  ) {}

  private getEncryptionKey(): string {
    const key = this.configService.get<string>('encryption.secretKey');
    if (!key) {
      throw new Error('Encryption key not found in configuration');
    }
    return key;
  }

  private async encryptUserData(
    userData: Partial<User>,
  ): Promise<Partial<User>> {
    const fieldsToEncrypt = ['email', 'cellPhone'];
    const encryptedData = { ...userData };

    for (const field of fieldsToEncrypt) {
      if (userData[field] && typeof userData[field] === 'string') {
        encryptedData[field] = await EncryptionUtil.encrypt(
          String(userData[field]),
          this.getEncryptionKey(),
        );
      }
    }

    return encryptedData;
  }

  private async decryptUserData(user: User): Promise<User> {
    const fieldsToDecrypt = ['email', 'cellPhone'];
    const decryptedUser: Partial<User> = user.toObject() as Partial<User>;

    for (const field of fieldsToDecrypt) {
      if (user[field]) {
        decryptedUser[field] = await EncryptionUtil.decrypt(
          user[field] as string,
          this.getEncryptionKey(),
        );
      }
    }

    return decryptedUser as User;
  }

  async create(
    createUserDto: CreateUserDto,
  ): Promise<{ message: string; data: Omit<User, 'password'> | null }> {
    try {
      const existingUser = await this.userModel
        .findOne({
          email: createUserDto.email,
        })
        .exec();
      if (existingUser) {
        return { message: ERROR.USER_EXISTS, data: null };
      }
      const { role, password, ...userData } = createUserDto;
      const createdUser = new this.userModel({
        ...userData,
        password,
        role,
      });
      const savedUser = await createdUser.save();
      return {
        message: ERROR.USER_ADD_SUCCESS,
        data: savedUser.toObject() as Omit<User, 'password'>,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof Error)
        throw error;
      throw new Error(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async findAll(): Promise<{ message: string; data: User[] }> {
    try {
      const users = await this.userModel.find().exec();
      return { message: 'Users retrieved successfully.', data: users };
    } catch (error) {
      console.error('Error getting user counts by role:', error);
      throw new Error(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async findOne(id: string): Promise<User> {
    try {
      const user = await this.userModel.findById(id).exec();
      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }
      return user;
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      throw new Error(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async findOneByEmail(
    email: string,
  ): Promise<(Partial<User> & { password?: string }) | null> {
    try {
      const user = await this.userModel
        .findOne({ email })
        .select('+password')
        .populate('role')
        .lean();
      return user;
    } catch (error) {
      console.error('Error getting user counts by role:', error);
      throw new Error(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async update(
    id: string,
    updateUserDto: UpdateUserDto,
    profile?: string,
  ): Promise<{ message: string; data: User | null }> {
    try {
      if (updateUserDto.email) {
        const existingUser = await this.userModel
          .findOne({ emailHash: updateUserDto.email, _id: { $ne: id } })
          .exec();
        if (existingUser) {
          return { message: ERROR.USER_EXISTS, data: null };
        }
      }
      const { password, ...userData } = updateUserDto;
      const updateObject: Partial<User> = { ...userData };
      if (profile) {
        updateObject['profile'] = profile;
      }
      if (password) {
        updateObject['password'] = await bcrypt.hash(password, 10);
      }
      const updatedUser = await this.userModel
        .findByIdAndUpdate(id, updateObject, { new: true })
        .exec();
      if (!updatedUser) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }
      return {
        message: 'User updated successfully.',
        data: updatedUser,
      };
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      throw new Error(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async remove(id: string): Promise<{ message: string; data: User }> {
    try {
      const deletedUser = await this.userModel.findByIdAndDelete(id).exec();
      if (!deletedUser) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }
      return { message: 'User deleted successfully.', data: deletedUser };
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      throw new Error(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async updatePasswordById(userId, hashedPassword) {
    try {
      const user = await this.userModel.findByIdAndUpdate(
        userId,
        { password: hashedPassword },
        { new: true },
      );
      if (!user) throw new Error('User not found');
      return user;
    } catch (error) {
      console.error('Error getting user counts by role:', error);
      throw new Error(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async getUserCountsByRole() {
    try {
      const counts = await this.userModel.aggregate([
        {
          $lookup: {
            from: 'roles',
            localField: 'role',
            foreignField: '_id',
            as: 'roleInfo',
          },
        },
        { $unwind: '$roleInfo' },
        {
          $group: {
            _id: '$roleInfo.name',
            count: { $sum: 1 },
          },
        },
      ]);
      let admin = 0,
        candidate = 0,
        employee = 0,
        total = 0;
      counts.forEach((item) => {
        if (item._id === 'Admin') admin = item.count;
        else if (item._id === 'Candidate') candidate = item.count;
        else if (item._id === 'Employee') employee = item.count;
        total += item.count;
      });
      return {
        total,
        admin,
        candidate,
        employee,
      };
    } catch (error) {
      console.error('Error getting user counts by role:', error);
      throw new Error(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async getUserById(id: string): Promise<User> {
    try {
      const user = await this.userModel
        .findById(id)
        .populate({ path: 'role', select: 'name' })
        .lean();
      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }
      return user;
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      throw new Error(ERROR.INTERNAL_SERVER_ERROR);
    }
  }
}
