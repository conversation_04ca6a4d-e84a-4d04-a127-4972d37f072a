import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function LandingHeader() {
  return (
    <header className="w-full bg-white/80 backdrop-blur-md shadow-sm fixed top-0 left-0 z-30">
      <div className="container mx-auto flex items-center justify-between py-4 px-4">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-2">
          <img
            src="/assets/logo/TalentLoop.svg"
            alt="TalentLoop Logo"
            className="h-8 w-auto"
          />
        </Link>
        {/* Nav Links */}
        <nav className="hidden md:flex gap-8 text-[#193E6C] font-medium">
          <Link href="/">Home</Link>
          <Link href="#about">About us</Link>
          <Link href="#pricing">Pricing</Link>
          <Link href="#contact">Contact us</Link>
        </nav>
        {/* Auth Buttons */}
        <div className="flex gap-2">
          <Link href="/choose-role">
            <Button className="bg-[#0858F8] text-white hover:bg-[#1e40af] border-2">
              Sign Up
            </Button>
          </Link>
          <Link href="/auth/login">
            <Button
              variant="outline"
              className="border-[#0858F8] text-[#0858F8] bg-white hover:bg-[#e0e7ff]"
            >
              Login
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
}
