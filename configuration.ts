import { join } from 'path';
import { readFileSync } from 'fs';
import * as yaml from 'js-yaml';
import { plainToInstance } from 'class-transformer';
import { validateSync } from 'class-validator';

class EnvironmentConfig {
  database: {
    mongodb: {
      uri: string;
      dbName: string;
      poolSize: number;
      timeoutMS: number;
      socketTimeoutMS: number;
      retryAttempts: number;
      retryDelay: number;
      autoIndex: boolean;
    };
  };
}

export default () => {
  const env = process.env.NODE_ENV || 'local';

  // ✅ Load from root-level config directory, not dist
  const configPath = join(
    process.cwd(),
    '../../config',
    `configuration.${env}.yml`,
  );
  console.log(`Loading configuration from: ${configPath}`);
  const config = yaml.load(readFileSync(configPath, 'utf8'));
  const validatedConfig = plainToInstance(EnvironmentConfig, config);

  const errors = validateSync(validatedConfig);
  if (errors.length > 0) {
    throw new Error(
      `Config validation error: ${JSON.stringify(errors, null, 2)}`,
    );
  }

  return validatedConfig;
};
