import {
  IsEmail,
  IsEnum,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>al,
  IsString,
  Min,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { Gender } from 'src/utils/constants';

export class UpdateUserDto {
  @IsString()
  @MinLength(1)
  @IsOptional()
  firstName?: string;

  @IsString()
  @MinLength(1)
  @IsOptional()
  lastName?: string;

  @IsString()
  @IsOptional()
  password?: string;

  @IsString()
  @MinLength(5)
  @IsOptional()
  zipCode?: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  age?: number;

  @IsEnum(Gender)
  @IsOptional()
  gender?: Gender;

  @IsEmail()
  @IsOptional()
  email?: string;

  @IsString()
  @MinLength(10)
  @IsOptional()
  cellPhone?: string;
}
