import React from 'react';
import { useUserActivity } from '@/hooks/useUserActivity';

export default function QuickActions({ actions }: { actions: string[] }) {
  const { trackButtonClick } = useUserActivity();

  const handleActionClick = (action: string) => {
    trackButtonClick(action, 'quick-actions');
    // You can add navigation logic here
    console.log(`Quick action clicked: ${action}`);
  };

  return (
    <div className="bg-[#FFF7F0] rounded-2xl shadow p-6 flex flex-col gap-3">
      <h2 className="font-semibold text-lg mb-4">Quick Actions</h2>
      {actions.map((action, i) => (
        <button
          key={i}
          onClick={() => handleActionClick(action)}
          className="flex items-center justify-between bg-white rounded-xl px-4 py-3 text-[#333] font-medium shadow hover:bg-[#F5F8FF] transition"
        >
          {action}
          <span className="text-[#377DFF]">&rarr;</span>
        </button>
      ))}
    </div>
  );
}
