import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { UserActivityRequest } from '@/types/userActivity';

const BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4000';

export const logUserActivity = createAsyncThunk<void, UserActivityRequest>(
  'userActivity/logUserActivity',
  async (activity, { rejectWithValue }) => {
    try {
      await fetch(`${BASE_URL}/user-activity/user-activity/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${typeof window !== 'undefined' ? localStorage.getItem('authToken') : ''}`,
        },
        body: JSON.stringify(activity),
        credentials: 'include',
      });
      return;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

interface UserActivityState {
  loading: boolean;
  error: string | null;
}

const initialState: UserActivityState = {
  loading: false,
  error: null,
};

const userActivitySlice = createSlice({
  name: 'userActivity',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(logUserActivity.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(logUserActivity.fulfilled, state => {
        state.loading = false;
      })
      .addCase(logUserActivity.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to log user activity';
      });
  },
});

export default userActivitySlice.reducer;
