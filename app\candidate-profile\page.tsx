'use client';
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useAppDispatch, useAppSelector } from '@/store';
import type { AppDispatch } from '@/store';

import {
  fetchCandidateProfile,
  fetchCompletion,
  uploadResume,
  manualResumeParse,
} from '@/store/slices/candidateProfileSlice';
import ProfileAvatar from '@/components/candidate-profile/ProfileAvatar';
import toast from 'react-hot-toast';
import type {
  CandidateProfile,
  Education,
  WorkExperience,
  StaticProfile,
} from '@/types/candidateProfile';
import AutoFillApplications from '@/components/candidate-profile/AutoFillApplications';
import ProfileTabs from '@/components/candidate-profile/ProfileTabs';
import BasicInformationTab from '@/components/candidate-profile/BasicInformationTab';
import EducationsTab from '@/components/candidate-profile/EducationsTab';
import WorkExperienceTab from '@/components/candidate-profile/WorkExperienceTab';
import SkillsTechnologyTab from '@/components/candidate-profile/SkillsTechnologyTab';
import CertificationsTab from '@/components/candidate-profile/CertificationsTab';
import CompletionSidebar from '@/components/candidate-profile/CompletionSidebar';
import Loader from '@/components/ui/Loader';

const tabs = [
  'Basic Information',
  'Educations',
  'Work Experience',
  'Skills & Technology',
  'Certifications',
  'Profile Scores',
];

const TrophyIcon = () => (
  <svg width="28" height="28" fill="none" viewBox="0 0 24 24">
    <path
      fill="#3C9DF6"
      d="M17 21H7a1 1 0 0 1 0-2h10a1 1 0 1 1 0 2ZM12 19a6 6 0 0 1-6-6V5a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v8a6 6 0 0 1-6 6Zm-4-14v8a4 4 0 1 0 8 0V5H8Z"
    />
  </svg>
);

const CircularProgress = ({
  value,
  max = 100,
}: {
  value: number;
  max?: number;
}) => {
  const radius = 54;
  const stroke = 10;
  const normalizedRadius = radius - stroke / 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const percent = Math.min(value / max, 1);
  const strokeDashoffset = circumference * (1 - percent);
  return (
    <svg height={radius * 2} width={radius * 2} className="rotate-[-90deg]">
      <circle
        stroke="#E3F0FF"
        fill="none"
        strokeWidth={stroke}
        cx={radius}
        cy={radius}
        r={normalizedRadius}
      />
      <circle
        stroke="#3C9DF6"
        fill="none"
        strokeWidth={stroke}
        cx={radius}
        cy={radius}
        r={normalizedRadius}
        strokeDasharray={circumference}
        strokeDashoffset={strokeDashoffset}
        strokeLinecap="round"
        style={{ transition: 'stroke-dashoffset 0.6s ease' }}
      />
    </svg>
  );
};

// Add normalization function before the CandidateProfile component
// Add a helper to check for valid URLs
function normalizeFormForManualParser(form: Record<string, unknown>) {
  const normalized: Record<string, unknown> = {
    ...form,
    educations: Array.isArray(form.educations)
      ? form.educations.map((edu: Education) => ({
          ...edu,
          startYear: edu.startYear ? String(edu.startYear) : '',
          endYear: edu.endYear ? String(edu.endYear) : '',
          grade: edu.grade ? String(edu.grade) : '',
          degree: edu.degree ? String(edu.degree) : '',
          fieldOfStudy: edu.fieldOfStudy ? String(edu.fieldOfStudy) : '',
          country: edu.country ? String(edu.country) : '',
        }))
      : [],
    workExperience: Array.isArray(form.workExperience)
      ? form.workExperience.map((work: WorkExperience) => {
          // Always use from_date/to_date, and remove from/to from payload
          const { from_date, to_date, ...rest } = work;
          return {
            ...rest,
            from_date: from_date || '',
            to_date: to_date || '',
            company: work.company ? String(work.company) : '',
            title: work.title ? String(work.title) : '',
            location: work.location ? String(work.location) : '',
            currentlyWorking: !!work.currentlyWorking,
            description: work.description ? String(work.description) : '',
          };
        })
      : [],
    image: form.image ? form.image : '', // Use empty string if not set
    resume: form.resume ? form.resume : '', // Use empty string if not set
    postalCode: form.postalCode ? String(form.postalCode) : '',
  };
  return normalized;
}

// Utility to deeply remove all null values from an object/array
function removeNulls(obj: unknown): unknown {
  if (Array.isArray(obj)) {
    return (obj as unknown[])
      .map(removeNulls)
      .filter(v => v !== null && v !== undefined);
  } else if (obj && typeof obj === 'object') {
    return Object.entries(obj as Record<string, unknown>).reduce(
      (acc, [key, value]) => {
        const cleaned = removeNulls(value);
        if (cleaned !== null && cleaned !== undefined) {
          (acc as Record<string, unknown>)[key] = cleaned;
        }
        return acc;
      },
      {} as Record<string, unknown>
    );
  }
  return obj;
}

export default function CandidateProfile() {
  // All hooks at the top
  const [activeTab, setActiveTab] = useState('Basic Information');
  const dispatch: AppDispatch = useAppDispatch();
  const user = useAppSelector((state: any) => state.auth.user);
  const userId = user?._id || user?.id || '';

  // Remove duplicate declaration
  // const dispatch = useAppDispatch();
  const candidateProfile = useAppSelector(state => state.candidateProfile.data);
  const isProfileLoading = useAppSelector(
    state => state.candidateProfile.loading
  );
  const isProfileError = useAppSelector(
    state => !!state.candidateProfile.error
  );
  const completionData = useAppSelector(
    state => state.candidateProfile.completion
  );

  // Initial form state must include all required CandidateProfile fields
  const [form, setForm] = useState<CandidateProfile>({
    userId: user?.id || '',
    fullName: '',
    email: '',
    phoneNumber: '',
    currentJobTitle: '',
    permanentAddress: '',
    country: '',
    city: '',
    bio: '',
    educations: [],
    workExperience: [],
    skills: [],
    certifications: [],
    image: '',
    resume: '',
    static_profile: {},
    postalCode: '',
  });
  const [editing, setEditing] = useState(false);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imageRemoved, setImageRemoved] = useState(false);
  const [resumeLoading, setResumeLoading] = useState(false);
  type ErrorFields = {
    fullName?: string;
    email?: string;
    currentJobTitle?: string;
    phoneNumber?: string;
    permanentAddress?: string;
    postalCode?: string;
  };
  const [errors, setErrors] = useState<ErrorFields>({});

  useEffect(() => {
    if (userId) {
      dispatch(fetchCandidateProfile(userId));
      dispatch(fetchCompletion(userId));
    }
  }, [userId, dispatch]);

  useEffect(() => {
    if (candidateProfile) {
      setProfileImage(candidateProfile.image || null);
      setForm({
        userId: candidateProfile.userId || '',
        fullName: candidateProfile.fullName || '',
        email: candidateProfile.email || '',
        phoneNumber: candidateProfile.phoneNumber || '',
        currentJobTitle: candidateProfile.currentJobTitle || '',
        permanentAddress: candidateProfile.permanentAddress || '',
        country: candidateProfile.country || '',
        city: candidateProfile.city || '',
        bio: candidateProfile.bio || '',
        educations: candidateProfile.educations || [],
        workExperience: candidateProfile.workExperience || [],
        skills: candidateProfile.skills || [],
        certifications: candidateProfile.certifications || [],
        image: candidateProfile.image || '',
        resume: candidateProfile.resume || '',
        static_profile: candidateProfile.static_profile || {},
        postalCode: candidateProfile.postalCode || '',
      });
    } else if (user) {
      setProfileImage(user.image || null);
      setForm({
        userId: user.id || '',
        fullName: user.firstName + ' ' + user.lastName,
        email: user.email,
        phoneNumber: '',
        currentJobTitle: '',
        permanentAddress: '',
        country: '',
        city: '',
        bio: '',
        educations: [],
        workExperience: [],
        skills: [],
        certifications: [],
        image: '',
        resume: '',
        static_profile: {
          technical_skills: 0,
          soft_skills: 0,
          experience: 0,
          cultural_fit: 0,
        },
        postalCode: '',
      });
    }
  }, [candidateProfile, user]);

  // Remove overloads for handleChange
  const handleChange = (
    field: keyof CandidateProfile,
    value: string | number | boolean | StaticProfile
  ) => {
    setForm((prev: CandidateProfile) => ({ ...prev, [field]: value }));
  };

  // Helper functions for dynamic fields
  const handleArrayChange = (
    field: keyof Pick<
      CandidateProfile,
      'educations' | 'workExperience' | 'certifications' | 'skills'
    >,
    idx: number,
    subfield: string,
    value: string | number | boolean
  ) => {
    setForm((prev: CandidateProfile) => ({
      ...prev,
      [field]: (prev[field] as unknown[]).map((item, i) =>
        i === idx
          ? typeof item === 'object' && item !== null
            ? { ...item, [subfield]: value }
            : item
          : item
      ),
    }));
  };
  const handleArrayAdd = (
    field: keyof Pick<
      CandidateProfile,
      'educations' | 'workExperience' | 'certifications' | 'skills'
    >,
    emptyObj: Record<string, unknown>
  ) => {
    setForm((prev: CandidateProfile) => ({
      ...prev,
      [field]: [...(prev[field] as unknown[]), emptyObj],
    }));
  };
  const handleArrayRemove = (
    field: keyof Pick<
      CandidateProfile,
      'educations' | 'workExperience' | 'certifications' | 'skills'
    >,
    idx: number
  ) => {
    setForm((prev: CandidateProfile) => ({
      ...prev,
      [field]: (prev[field] as unknown[]).filter((_, i) => i !== idx),
    }));
  };

  const handleAvatarChange = (file: File, croppedDataUrl?: string) => {
    setImageFile(file);
    if (croppedDataUrl) {
      setProfileImage(croppedDataUrl);
    } else {
      setProfileImage(URL.createObjectURL(file));
    }
    setImageRemoved(false);
  };
  const handleAvatarRemove = () => {
    setImageFile(null);
    setProfileImage(null);
    setImageRemoved(true);
  };

  // Resume upload handler
  const handleResumeUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !userId) return;
    setResumeLoading(true);
    try {
      const dataRaw = await dispatch(uploadResume({ file, userId })).unwrap();
      let cleanedData: Partial<CandidateProfile> = {};
      if (
        typeof dataRaw === 'object' &&
        dataRaw !== null &&
        (dataRaw as { status?: string }).status === 'success' &&
        'data' in dataRaw &&
        typeof (dataRaw as { data?: unknown }).data === 'object' &&
        (dataRaw as { data?: unknown }).data !== null
      ) {
        // Map API fields to form state, removing nulls
        const cleanedDataRaw = removeNulls(
          (dataRaw as { data?: unknown }).data
        );
        if (typeof cleanedDataRaw === 'object' && cleanedDataRaw !== null) {
          cleanedData = cleanedDataRaw as Partial<CandidateProfile>;
        }
        setForm((prev: CandidateProfile) => ({
          ...prev,
          ...cleanedData,
          educations: Array.isArray(cleanedData.educations)
            ? cleanedData.educations
            : [],
          workExperience: Array.isArray(cleanedData.workExperience)
            ? cleanedData.workExperience
            : [],
          certifications: Array.isArray(cleanedData.certifications)
            ? cleanedData.certifications
            : [],
          skills: Array.isArray(cleanedData.skills) ? cleanedData.skills : [],
        }));
        toast.success(
          'Resume details loaded into the form. Please review and fill in any missing or incorrect details. Click Save to confirm, even if all details are correct.'
        );
        setEditing(true);
      } else {
        toast.error('Could not parse resume. Please try another file.');
      }
    } catch (err) {
      toast.error('Failed to upload or parse resume.');
    } finally {
      setResumeLoading(false);
    }
  };

  // Validation
  const validate = () => {
    const newErrors: ErrorFields = {};
    if (!(form.fullName || '').trim())
      newErrors.fullName = 'Full Name is required';
    if (!(form.email || '').trim()) newErrors.email = 'Email is required';
    if (!(form.currentJobTitle || '').trim())
      newErrors.currentJobTitle = 'Current Job Title is required';
    if (!(form.phoneNumber || '').trim())
      newErrors.phoneNumber = 'Phone Number is required';
    if (!(form.permanentAddress || '').trim())
      newErrors.permanentAddress = 'Permanent Address is required';
    if (!(form.postalCode || '').trim())
      newErrors.postalCode = 'Postal Code is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Save handler override for manual resume save
  const handleSave = async () => {
    if (!validate()) return;
    try {
      const data: Record<string, unknown> = { ...form };
      // Ensure string fields are empty string if not set
      data.image = data.image ? data.image : '';
      data.resume = data.resume ? data.resume : '';
      // Ensure workExperience date fields are omitted if empty
      if (Array.isArray(data.workExperience)) {
        data.workExperience = (data.workExperience as WorkExperience[]).map(
          (work: WorkExperience) => {
            const newWork = { ...work };
            if (!newWork.from_date) delete newWork.from_date;
            if (!newWork.to_date) delete newWork.to_date;
            return newWork;
          }
        );
      }
      // Ensure workExperience date fields are omitted unless valid date strings
      if (Array.isArray(data.workExperience)) {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        data.workExperience = (data.workExperience as WorkExperience[]).map(
          (work: WorkExperience) => {
            const newWork = { ...work };
            if (
              !newWork.from_date ||
              typeof newWork.from_date !== 'string' ||
              !dateRegex.test(newWork.from_date)
            ) {
              delete newWork.from_date;
            }
            if (
              !newWork.to_date ||
              typeof newWork.to_date !== 'string' ||
              !dateRegex.test(newWork.to_date)
            ) {
              delete newWork.to_date;
            }
            return newWork;
          }
        );
      }

      if (imageFile || imageRemoved) {
        // Use FormData for file upload
        const formData = new FormData();
        Object.entries(data).forEach(([key, value]) => {
          if (Array.isArray(value)) {
            formData.append(key, JSON.stringify(value));
          } else if ((key === 'image' || key === 'resume') && !value) {
            formData.append(key, ''); // Use empty string
          } else {
            formData.append(key, value as any);
          }
        });
        if (imageFile) formData.append('image', imageFile);
        if (imageRemoved) formData.append('image', '');
      }
      if (
        form.fullName &&
        form.email &&
        form.currentJobTitle &&
        form.phoneNumber &&
        form.permanentAddress &&
        form.postalCode
      ) {
        const normalizedForm = normalizeFormForManualParser(
          form as unknown as Record<string, unknown>
        );
        if (candidateProfile && candidateProfile._id) {
          const result = await dispatch(
            manualResumeParse({
              form: normalizedForm as Partial<CandidateProfile>,
              userId,
            })
          ).unwrap();

          // Only show success if API call was successful
          if (result && result.status === 'success') {
            toast.success('Profile updated!');
          } else {
            toast.error(result?.message || 'Failed to save profile');
          }
        } else {
          toast.error('Failed to save profile');
        }
      }
      setEditing(false);
      setImageRemoved(false);
      dispatch(fetchCandidateProfile(userId));
    } catch (e) {
      toast.error('Failed to save profile');
    }
  };

  // Remove hasCompletion and completionChecklist logic
  let completionPercent = 0;
  if (
    completionData &&
    typeof completionData === 'object' &&
    'completionPercentage' in completionData &&
    typeof (completionData as { completionPercentage?: number })
      .completionPercentage === 'number'
  ) {
    completionPercent = (completionData as { completionPercentage?: number })
      .completionPercentage!;
  }

  // Animation state for progress (must be before any return)
  const [animatedPercent, setAnimatedPercent] = useState(0);
  useEffect(() => {
    if (completionPercent !== undefined) {
      const duration = 1.2; // seconds
      const startTime = performance.now();
      function animate(now: number) {
        const elapsed = (now - startTime) / 1000;
        const progress = Math.min(elapsed / duration, 1);
        setAnimatedPercent(Math.floor(progress * completionPercent));
        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          setAnimatedPercent(completionPercent);
        }
      }
      requestAnimationFrame(animate);
    }
  }, [completionPercent]);

  if (isProfileLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Loader />
      </div>
    );
  }
  if (isProfileError) {
    return (
      <div className="flex items-center justify-center min-h-screen text-red-600">
        Error loading profile.
      </div>
    );
  }

  return (
    <div className="w-full max-w-full min-h-screen bg-gradient-to-br from-[#E3F0FF] to-[#F6F9FF] py-10 px-2 overflow-x-hidden">
      <div className="w-full max-w-7xl mx-auto flex flex-col lg:flex-row gap-4 lg:gap-8">
        {/* Main Card and Tabs */}
        <div className="flex-1 flex flex-col w-full">
          <div className="rounded-3xl bg-white shadow-lg p-4 md:p-8 w-full">
            {/* Profile Card */}
            <div className="flex flex-col md:flex-row md:items-center gap-6 mb-6">
              <div className="flex flex-col items-center md:items-start gap-2">
                <ProfileAvatar
                  image={profileImage}
                  editing={editing}
                  onChange={handleAvatarChange}
                  onRemove={handleAvatarRemove}
                />
              </div>
              <div className="flex-1 flex flex-col gap-1">
                <div className="text-2xl font-bold text-[#193E6C]">
                  {form.fullName}
                </div>
                <div className="text-[#3C9DF6] font-medium">
                  {form.currentJobTitle}
                </div>
                <div className="text-[#193E6C] text-sm">
                  {form.city || ''} {form.country || ''}
                </div>
              </div>
              <div>
                {!editing ? (
                  <Button variant="default" onClick={() => setEditing(true)}>
                    Edit
                  </Button>
                ) : (
                  <Button
                    variant="default"
                    onClick={handleSave}
                    disabled={isProfileLoading}
                  >
                    Save
                  </Button>
                )}
              </div>
            </div>
            {/* Auto Fill Applications */}
            <AutoFillApplications
              editing={editing}
              resumeLoading={resumeLoading}
              onResumeUpload={handleResumeUpload}
            />
            {/* Tabs */}
            <ProfileTabs
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />
            {/* Tab Content */}
            <div className="bg-[#F6F9FF] rounded-2xl p-4 md:p-6 w-full">
              {activeTab === 'Basic Information' && (
                <BasicInformationTab
                  form={form}
                  editing={editing}
                  errors={errors}
                  handleChange={handleChange}
                />
              )}
              {activeTab === 'Educations' && (
                <EducationsTab
                  educations={form.educations}
                  editing={editing}
                  handleArrayAdd={handleArrayAdd}
                  handleArrayRemove={handleArrayRemove}
                  handleArrayChange={handleArrayChange}
                />
              )}
              {activeTab === 'Work Experience' && (
                <WorkExperienceTab
                  workExperience={form.workExperience}
                  editing={editing}
                  handleArrayAdd={handleArrayAdd}
                  handleArrayRemove={handleArrayRemove}
                  handleArrayChange={handleArrayChange}
                />
              )}
              {activeTab === 'Skills & Technology' && (
                <SkillsTechnologyTab
                  skills={form.skills}
                  editing={editing}
                  handleArrayAdd={handleArrayAdd}
                  handleArrayRemove={handleArrayRemove}
                  handleArrayChange={handleArrayChange}
                  onChange={skills =>
                    editing && setForm(prev => ({ ...prev, skills }))
                  }
                />
              )}
              {activeTab === 'Certifications' && (
                <CertificationsTab
                  certifications={form.certifications}
                  editing={editing}
                  handleArrayAdd={handleArrayAdd}
                  handleArrayRemove={handleArrayRemove}
                  handleArrayChange={handleArrayChange}
                />
              )}
              {activeTab === 'Profile Scores' && (
                <div className="bg-white rounded-2xl shadow-md p-4 md:p-8 w-full max-w-full">
                  <div className="flex items-center gap-3 mb-6 md:mb-8">
                    <TrophyIcon />
                    <h2 className="text-lg md:text-2xl font-bold text-[#193E6C]">
                      Profile Scores
                    </h2>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-1 text-[#193E6C]">
                        Technical Skills
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={form.static_profile?.technical_skills || 0}
                        readOnly={!editing}
                        onChange={e =>
                          handleChange('static_profile', {
                            ...form.static_profile,
                            technical_skills: parseInt(e.target.value) || 0,
                          })
                        }
                        className="w-full bg-[#E3F0FF] rounded-lg px-4 py-3 text-[#193E6C] font-medium text-base md:text-lg focus:ring-2 focus:ring-blue-200 outline-none"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1 text-[#193E6C]">
                        Soft Skills
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={form.static_profile?.soft_skills || 0}
                        readOnly={!editing}
                        onChange={e =>
                          handleChange('static_profile', {
                            ...form.static_profile,
                            soft_skills: parseInt(e.target.value) || 0,
                          })
                        }
                        className="w-full bg-[#E3F0FF] rounded-lg px-4 py-3 text-[#193E6C] font-medium text-base md:text-lg focus:ring-2 focus:ring-blue-200 outline-none"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1 text-[#193E6C]">
                        Experience
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={form.static_profile?.experience || 0}
                        readOnly={!editing}
                        onChange={e =>
                          handleChange('static_profile', {
                            ...form.static_profile,
                            experience: parseInt(e.target.value) || 0,
                          })
                        }
                        className="w-full bg-[#E3F0FF] rounded-lg px-4 py-3 text-[#193E6C] font-medium text-base md:text-lg focus:ring-2 focus:ring-blue-200 outline-none"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1 text-[#193E6C]">
                        Cultural Fit
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={form.static_profile?.cultural_fit || 0}
                        readOnly={!editing}
                        onChange={e =>
                          handleChange('static_profile', {
                            ...form.static_profile,
                            cultural_fit: parseInt(e.target.value) || 0,
                          })
                        }
                        className="w-full bg-[#E3F0FF] rounded-lg px-4 py-3 text-[#193E6C] font-medium text-base md:text-lg focus:ring-2 focus:ring-blue-200 outline-none"
                      />
                    </div>
                  </div>
                  <div className="mt-8 md:mt-10 flex flex-col items-center">
                    <div className="text-base md:text-lg font-semibold text-[#193E6C] mb-2">
                      Total Score
                    </div>
                    <div className="w-24 h-24 md:w-32 md:h-32 flex items-center justify-center relative">
                      <CircularProgress
                        value={Math.min(
                          (form.static_profile?.technical_skills || 0) +
                            (form.static_profile?.soft_skills || 0) +
                            (form.static_profile?.experience || 0) +
                            (form.static_profile?.cultural_fit || 0),
                          100
                        )}
                      />
                      <span className="absolute text-2xl md:text-3xl font-bold text-blue-600">
                        {Math.min(
                          (form.static_profile?.technical_skills || 0) +
                            (form.static_profile?.soft_skills || 0) +
                            (form.static_profile?.experience || 0) +
                            (form.static_profile?.cultural_fit || 0),
                          100
                        )}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500 mt-2">
                      Maximum total score is capped at 100
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        {/* Sidebar */}
        <CompletionSidebar
          completionData={completionData}
          animatedPercent={animatedPercent}
        />
      </div>
    </div>
  );
}
