import React from 'react';

interface ProgressBarProps {
  percent: number;
  color: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ percent, color }) => (
  <div className="w-full h-2 bg-gray-200 rounded-full mt-2 mb-2 relative">
    <div
      className={`h-2 rounded-full ${color}`}
      style={{ width: `${percent}%` }}
    ></div>
    <span className="absolute right-0 -top-6 text-xs text-gray-500">
      {percent}%
    </span>
  </div>
);

export default ProgressBar;
