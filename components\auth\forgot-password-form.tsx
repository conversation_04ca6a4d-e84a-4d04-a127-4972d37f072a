import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Mail } from 'lucide-react';

interface ForgotPasswordFormProps {
  onSubmit: (data: { email: string }) => void;
  isLoading?: boolean;
  message?: string;
  error?: string;
}

export function ForgotPasswordForm({
  onSubmit,
  isLoading,
  message,
  error,
}: ForgotPasswordFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<{ email: string }>();

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="email" className="text-sm font-medium">
          Email
        </Label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            className={
              'pl-12 h-12 rounded-lg text-base shadow-sm' +
              (errors.email ? ' border-red-500 focus-visible:ring-red-500' : '')
            }
            {...register('email', { required: 'Email is required' })}
          />
        </div>
        {errors.email && (
          <p className="text-xs text-red-500">{errors.email.message}</p>
        )}
      </div>
      <Button
        type="submit"
        disabled={isLoading}
        className="w-full h-12 rounded-full bg-gradient-to-r from-[#1976F6] to-[#4F8DFD] text-lg font-bold border-0 shadow-md hover:from-[#0858F8] hover:to-[#1976F6]"
      >
        {isLoading ? 'Sending...' : 'Send Link'}
      </Button>
      {message && <p className="text-green-600 text-sm mt-2">{message}</p>}
      {error && <p className="text-red-600 text-sm mt-2">{error}</p>}
    </form>
  );
}
