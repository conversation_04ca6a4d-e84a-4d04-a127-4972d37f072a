import {
  <PERSON>String,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsBoolean,
  IsN<PERSON>ber,
  Min,
} from 'class-validator';

export class CreateJobDto {
  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsNotEmpty()
  company: string;

  @IsString()
  @IsNotEmpty()
  location: string;

  @IsBoolean()
  remote_ok: boolean;

  @IsNumber()
  @Min(0)
  salary_min: number;

  @IsNumber()
  @Min(0)
  salary_max: number;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsOptional()
  @IsString()
  employerId: string;

  @IsString()
  @IsNotEmpty()
  domain: string;

  @IsOptional()
  @IsString({ each: true })
  skills?: string[];

  @IsObject()
  criteria_weights: {
    technical_skills: number;
    soft_skills: number;
    experience: number;
    cultural_fit: number;
  };
}
