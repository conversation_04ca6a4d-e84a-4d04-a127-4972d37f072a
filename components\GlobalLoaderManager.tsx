'use client';
import Loader from './ui/Loader';
import { useAppSelector, useAppDispatch } from '@/store';
import { usePathname } from 'next/navigation';
import { useEffect, useRef } from 'react';
import { startLoading, stopLoading } from '@/store/slices/loadingSlice';

const GlobalLoaderManager = () => {
  const isLoading = useAppSelector(state => state.loading.isLoading);
  const dispatch = useAppDispatch();
  const pathname = usePathname();
  const prevPath = useRef(pathname);

  useEffect(() => {
    if (prevPath.current !== pathname) {
      dispatch(startLoading());
      setTimeout(() => {
        dispatch(stopLoading());
      }, 400);
      prevPath.current = pathname;
    }
  }, [pathname, dispatch]);

  return isLoading ? <Loader /> : null;
};

export default GlobalLoaderManager;
