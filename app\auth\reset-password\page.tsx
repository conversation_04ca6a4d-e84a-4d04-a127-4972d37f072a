'use client';

import { useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { ResetPasswordForm } from '@/components/auth/reset-password-form';
import { useAppDispatch, useAppSelector } from '@/store';
import { resetPassword } from '@/store/slices/authSlice';
import toast from 'react-hot-toast';
import Image from 'next/image';
import Link from 'next/link';
import type { ResetPasswordResponse } from '@/types/auth';

export default function ResetPasswordPage() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(state => state.auth.isLoading);
  const [expired, setExpired] = useState(false);
  const router = useRouter();

  const handleSubmit = async (data: {
    newPassword: string;
    confirmPassword: string;
  }) => {
    if (!token) {
      toast.error('Invalid or missing token.');
      return;
    }
    if (data.newPassword !== data.confirmPassword) {
      toast.error('Passwords do not match.');
      return;
    }
    try {
      const resultAction = await dispatch(
        resetPassword({
          token,
          newPassword: data.newPassword,
        })
      );
      const res = resultAction.payload as ResetPasswordResponse;
      if (resetPassword.fulfilled.match(resultAction)) {
        toast.success(res.message || 'Password reset successful!');
        setTimeout(() => {
          router.replace('/auth/login');
        }, 2000);
      } else {
        throw res;
      }
    } catch (err: unknown) {
      let errorMessage = 'Failed to reset password.';
      if (
        err &&
        typeof err === 'object' &&
        'data' in err &&
        err.data &&
        typeof err.data === 'object' &&
        'message' in err.data &&
        typeof err.data.message === 'string'
      ) {
        const msg = err.data.message as string;
        if (msg.toLowerCase().includes('expire')) {
          toast.error('Reset link expired. Please request a new one.');
          setExpired(true);
          return;
        }
        errorMessage = msg;
      }
      toast.error(errorMessage);
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Side */}
      <div className="hidden md:flex flex-col justify-center items-center w-1/2 bg-gradient-to-b from-[#1976F6] to-[#4F8DFD] rounded-tr-[40px] rounded-br-[40px] p-12 relative">
        <h1 className="text-white text-4xl font-extrabold mb-4 text-center drop-shadow-lg">
          Where Top Talents Meet Opportunities
        </h1>
        <p className="text-white text-lg text-center opacity-80 max-w-md">
          TalentLoop connects top candidates with leading employers using
          AI-powered matching. Discover opportunities, streamline hiring, and
          unlock your career potential—all in one platform.
        </p>
      </div>
      {/* Right Side */}
      <div className="flex-1 flex flex-col min-h-screen bg-white">
        {/* Top Bar */}
        <div className="flex items-center justify-between px-10 pt-8">
          <Image
            src="/assets/logo/TalentLoop.svg"
            alt="TalentLoop Logo"
            width={140}
            height={36}
          />
          <nav className="flex gap-8 text-[#1976F6] font-medium text-base">
            <Link href="/">Home</Link>
            <a href="#">About us</a>
            <a href="#">Pricing</a>
            <a href="#">Contact Us</a>
          </nav>
        </div>
        {/* Main Content */}
        <div className="flex flex-1 flex-col justify-center items-center py-8">
          <div className="w-full max-w-md p-8 rounded-2xl shadow-2xl border-0 bg-white">
            <div className="text-3xl font-extrabold text-gray-900 mb-2 text-left">
              Reset Password
            </div>
            <div className="text-lg text-gray-500 mb-6 text-left">
              Enter your new password below.
            </div>
            <ResetPasswordForm onSubmit={handleSubmit} isLoading={isLoading} />
            {expired && (
              <div className="mt-4 text-center">
                <a
                  href="/auth/forgot-password"
                  className="text-[#1976F6] font-semibold hover:underline"
                >
                  Request new reset link
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
