'use client';

import { LoginForm } from '@/components/auth/login-form';
import { useAppDispatch, useAppSelector } from '@/store';
import { login } from '@/store/slices/authSlice';
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import type { AuthResponse } from '@/types/auth';

export default function LoginPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(state => state.auth.isLoading);
  const handleLogin = async (data: { email: string; password: string }) => {
    try {
      const resultAction = await dispatch(login(data));
      const result = resultAction.payload as AuthResponse;
      if (login.fulfilled.match(resultAction)) {
        toast.success('Login successful!');
        if (result?.data?.access_token) {
          localStorage.setItem('authToken', result.data.access_token);
        }
        const user = result?.data?.user;
        if (user?.roleName === 'employee' && user?.domainVerified === false) {
          setTimeout(() => {
            router.replace('/verify-domain');
          }, 1500);
          return;
        }
        setTimeout(() => {
          router.replace('/dashboard');
        }, 1500);
        console.log('Login success:', result);
      } else {
        throw result;
      }
    } catch (err: unknown) {
      if (err && typeof err === 'object' && 'data' in err) {
        toast.error(
          (err as { data?: { message?: string } })?.data?.message ||
            'Login failed.'
        );
      } else {
        toast.error('Login failed.');
      }
      console.error('Login error:', err);
    }
  };

  const handleLinkedInSignIn = async () => {
    await new Promise(resolve => setTimeout(resolve, 1500));
    console.log('LinkedIn sign in');
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Side */}
      <div className="hidden md:flex flex-col justify-center items-center w-1/2 bg-gradient-to-b from-[#1976F6] to-[#4F8DFD] rounded-tr-[40px] rounded-br-[40px] p-12 relative">
        <h1 className="text-white text-4xl font-extrabold mb-4 text-center drop-shadow-lg">
          Where Top Talents Meet Opportunities
        </h1>
        <p className="text-white text-lg text-center opacity-80 max-w-md">
          TalentLoop connects top candidates with leading employers using
          AI-powered matching. Discover opportunities, streamline hiring, and
          unlock your career potential—all in one platform.
        </p>
      </div>
      {/* Right Side */}
      <div className="flex-1 flex flex-col min-h-screen bg-white">
        {/* Top Bar */}
        <div className="flex items-center justify-between px-10 pt-8">
          <Image
            src="/assets/logo/TalentLoop.svg"
            alt="TalentLoop Logo"
            width={140}
            height={36}
          />
          <nav className="flex gap-8 text-[#1976F6] font-medium text-base">
            <Link href="/">Home</Link>
            <a href="#">About us</a>
            <a href="#">Pricing</a>
            <a href="#">Contact Us</a>
          </nav>
        </div>
        {/* Main Content */}
        <div className="flex flex-1 flex-col justify-center items-center py-8">
          <LoginForm
            onSubmit={handleLogin}
            onLinkedInSignIn={handleLinkedInSignIn}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  );
}
