import React from 'react';

interface StatsCardProps {
  label: string;
  value: number;
  icon: React.ReactNode;
}

const StatsCard: React.FC<StatsCardProps> = ({ label, value, icon }) => (
  <div className="flex flex-col items-center bg-white rounded-xl shadow p-6 min-w-[180px]">
    <div className="w-12 h-12 flex items-center justify-center bg-orange-100 rounded-full mb-2 text-2xl">
      {icon}
    </div>
    <div className="text-2xl font-bold">{value}</div>
    <div className="text-gray-500 text-sm">{label}</div>
  </div>
);

export default StatsCard;
