'use client';
import { useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import toast from 'react-hot-toast';
import Loader from '@/components/ui/Loader';

export default function GoogleCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const error = searchParams.get('error');
  const hasHandled = useRef(false);

  useEffect(() => {
    if (hasHandled.current) return;
    hasHandled.current = true;

    if (token) {
      localStorage.setItem('authToken', token);
      toast.success(
        'Sign in with Google successful! Redirecting to dashboard...'
      );
      setTimeout(() => {
        router.replace('/dashboard');
      }, 1500);
    } else if (error) {
      toast.error(error || 'Google login failed. Please try again.');
    } else {
      toast.error('No token found in URL. Please try logging in again.');
    }
  }, [router, token, error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <Loader />
    </div>
  );
}
