import React from 'react';
import TopBanner from './candidate/TopBanner';
import JobMatches from './candidate/JobMatches';
import QuickActions from './candidate/QuickActions';
import CalendarWidget from './candidate/CalendarWidget';
import MatchJourney from './candidate/MatchJourney';
import RecentActivities from './candidate/RecentActivities';
import UpcomingInterviews from './candidate/UpcomingInterviews';
import { useAppSelector, useAppDispatch } from '@/store';
import { fetchCompletion } from '@/store/slices/candidateProfileSlice';

const aiInsights = [
  {
    company: 'Dropbox',
    role: 'Brand Designer',
    location: 'Paris, France',
    match: '94%',
    tags: ['Full-Time', 'Marketing', 'Design'],
  },
  {
    company: 'Nomad',
    role: 'Social Media Assistant',
    location: 'Paris, France',
    match: '94%',
    tags: ['Full-Time', 'Marketing', 'Content Creation'],
  },
];

const quickActions = [
  'Update your profile',
  'Dream Job Setup',
  'Get AI Career Guidance',
  'My Applications',
];

const matchJourney = [
  {
    label: 'Matched to 4 employers this week',
    desc: 'Your profile attracted attention from top companies',
    time: 'This week',
    color: 'green',
  },
  {
    label: '2 companies viewed your profile',
    desc: 'Dropbox and Tech Corp showed interest',
    time: '2 days ago',
    color: 'blue',
  },
  {
    label: '1 interview request received',
    desc: 'Social Media Assistant position at Nomad',
    time: '2hr ago',
    color: 'yellow',
  },
  {
    label: 'Next interview scheduled',
    desc: 'HR Manager interview tomorrow at 10:00 AM',
    time: 'Tomorrow',
    color: 'red',
  },
];

const recentActivities = [
  'Matched with Dropbox for Brand Designer',
  'Nomad viewed your profile',
  'Interview request from Packer for HR Manager',
  'Matched with StartupX for UX Designer',
];

const upcomingInterviews = [
  {
    role: 'Social Media Assistant',
    company: 'Tech Corp',
    location: 'Paris, France',
    time: 'Tomorrow, 10:00 AM',
  },
  {
    role: 'HR Manager',
    company: 'Tech Corp',
    location: 'Paris, France',
    time: 'Tomorrow, 10:00 AM',
  },
];

const calendarEvents = [
  { label: 'HR Manager Interview' },
  { label: 'Brand Designer Interview' },
];

export default function CandidateDashboard({ user }: { user: unknown }) {
  const typedUser = user as any;
  const userId = typedUser?._id || typedUser?.id || '';
  const dispatch = useAppDispatch();
  const completionData = useAppSelector(
    state => state.candidateProfile.completion
  );
  React.useEffect(() => {
    if (userId) {
      dispatch(fetchCompletion(userId));
    }
  }, [userId, dispatch]);
  let completionPercent = 0;
  if (
    completionData &&
    typeof completionData === 'object' &&
    'completionPercentage' in completionData &&
    typeof (completionData as { completionPercentage?: number })
      .completionPercentage === 'number'
  ) {
    completionPercent = (completionData as { completionPercentage: number })
      .completionPercentage;
  }
  const stats = [
    { label: 'Profile Completion', value: `${completionPercent}%`, icon: '👤' },
    { label: 'Interview Requests', value: '3', icon: '✉️' },
    { label: 'Job Matches', value: '47', icon: '📅' },
  ];
  return (
    <div className="bg-gradient-to-b from-[#e6f0ff] to-white min-h-screen w-full">
      <TopBanner
        user={user}
        profileCompletion={completionPercent}
        stats={stats}
      />
      <div className="max-w-6xl mx-auto mt-10 grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="md:col-span-2 flex flex-col gap-6">
          <JobMatches aiInsights={aiInsights} />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <MatchJourney journey={matchJourney} />
            <RecentActivities activities={recentActivities} />
          </div>
        </div>
        {/* Right Column */}
        <div className="flex flex-col gap-6">
          <QuickActions actions={quickActions} />
          <CalendarWidget
            month="September"
            year={2021}
            events={calendarEvents}
          />
          <UpcomingInterviews interviews={upcomingInterviews} />
        </div>
      </div>
    </div>
  );
}
