import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type AdminProfileDocument = AdminProfile & Document;

@Schema({ timestamps: true })
export class AdminProfile {
  // Reference to the user collection
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  // Store image name
  @Prop({ required: false })
  image?: string;
}

export const AdminProfileSchema = SchemaFactory.createForClass(AdminProfile);
