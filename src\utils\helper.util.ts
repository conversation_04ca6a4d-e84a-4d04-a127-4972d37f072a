import { join } from 'path';
import { ENVIRONMENT } from './constants';
import { doubleCsrf } from 'csrf-csrf';
import { ConfigService } from '@nestjs/config';

export function getConfigPath(env = 'local'): string {
  const configPath = join(process.cwd(), 'config', `configuration.${env}.yml`);
  console.log('Resolved config path:', configPath);
  return configPath;
}

export function createDoubleCsrfOptions(configService: ConfigService) {
  return {
    getSecret: () => configService.get<string>('csrf.secretKey'),
    cookieName: 'x-csrf-token',
    cookieOptions: {
      httpOnly: false,
      secure: ENVIRONMENT.includes(process.env.NODE_ENV),
      sameSite: ENVIRONMENT.includes(process.env.NODE_ENV)
        ? ('lax' as const)
        : ('strict' as const),
      domain: process.env.DOMAIN_NAME,
    },
    getTokenFromRequest: (req) =>
      req.headers['x-csrf-token'] || req.cookies['x-csrf-token'],
    getSessionIdentifier: (req): string => {
      return req.session?.userId || req.cookies?.['session-id'] || 'anonymous';
    },
  };
}

const csrfOptions = createDoubleCsrfOptions(new ConfigService());
export const { generateCsrfToken, doubleCsrfProtection } =
  doubleCsrf(csrfOptions);
