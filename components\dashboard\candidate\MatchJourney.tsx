import React from 'react';

interface JourneyItem {
  label: string;
  desc: string;
  time: string;
  color: string;
}

export default function MatchJourney({ journey }: { journey: JourneyItem[] }) {
  return (
    <div className="bg-white rounded-2xl shadow p-6">
      <h2 className="font-semibold text-lg mb-4">Match Journey</h2>
      <ul className="space-y-4">
        {journey.map((item, i) => (
          <li key={i} className="flex items-start gap-3">
            <span
              className={`w-3 h-3 mt-1 rounded-full bg-${item.color}-400 flex-shrink-0`}
            />
            <div>
              <div className="font-medium">{item.label}</div>
              <div className="text-xs text-gray-500">{item.desc}</div>
              <div className="text-xs text-gray-400 mt-1">{item.time}</div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
