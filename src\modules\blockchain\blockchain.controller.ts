import { Controller, UseGuards, Post, Body } from '@nestjs/common';
import { BlockchainService } from './blockchain.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@Controller('resume')
export class BlockchainController {
  constructor(private readonly blockchainService: BlockchainService) {}

  @UseGuards(JwtAuthGuard)
  @Post('sign')
  async signResume(@Body() body: { resume: Record<string, any> }) {
    return this.blockchainService.storeResumeHash(body.resume);
  }

  @UseGuards(JwtAuthGuard)
  @Post('verify')
  async verifyResume(
    @Body() body: { userAddress: string; resume: Record<string, any> },
  ) {
    const verified = await this.blockchainService.verifyResume(
      body.userAddress,
      body.resume,
    );
    return { verified };
  }
}
