import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import type {
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
} from '@/types/auth';
import type { User } from '@/types/user';

import api from '@/lib/api';

const BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4000';

// Utility to get auth token from localStorage
const getAuthToken = () =>
  typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;

// Helper to set up headers
const getHeaders = (isJson = true) => {
  const headers: Record<string, string> = {};
  const token = getAuthToken();
  if (token) headers['Authorization'] = `Bearer ${token}`;
  if (isJson) headers['Content-Type'] = 'application/json';
  return headers;
};

export const login = createAsyncThunk<AuthResponse, LoginRequest>(
  'auth/login',
  async (body, { dispatch, rejectWithValue }) => {
    try {
      const { data } = await api.post(`${BASE_URL}/auth/login`, body, {
        headers: getHeaders(),
        withCredentials: true,
      });
      // Set token BEFORE fetching user-details
      if (data?.data?.access_token) {
        localStorage.setItem('authToken', data.data.access_token);
      }
      // Now token is available for getProfile
      await dispatch(getProfile());
      return data;
    } catch (err: any) {
      return rejectWithValue(err);
    }
  }
);

export const register = createAsyncThunk<AuthResponse, RegisterRequest>(
  'auth/register',
  async (body, { dispatch, rejectWithValue }) => {
    try {
      const { data } = await api.post(`${BASE_URL}/auth/register`, body, {
        headers: getHeaders(),
        withCredentials: true,
      });
      // Fetch user details after register
      await dispatch(getProfile());
      return data;
    } catch (err: any) {
      return rejectWithValue(err);
    }
  }
);

export const googleSignup = createAsyncThunk<
  AuthResponse,
  { role: string; idToken: string }
>('auth/google/signup', async (body, { rejectWithValue }) => {
  try {
    const { data } = await api.post(`${BASE_URL}/auth/google/signup`, body, {
      headers: getHeaders(),
      withCredentials: true,
    });
    return data;
  } catch (err: any) {
    return rejectWithValue(err);
  }
});

export const googleSignin = createAsyncThunk<
  AuthResponse,
  { idToken: string }
>('auth/google/signin', async (body, { rejectWithValue }) => {
  try {
    const { data } = await api.post(`${BASE_URL}/auth/google/signin`, body, {
      headers: getHeaders(),
      withCredentials: true,
    });
    return data;
  } catch (err: any) {
    return rejectWithValue(err);
  }
});

// Keep the old method for backward compatibility
export const getGoogleResponse = googleSignup;

export const forgotPassword = createAsyncThunk<
  ForgotPasswordResponse,
  ForgotPasswordRequest
>('auth/forgotPassword', async (body, { rejectWithValue }) => {
  try {
    const { data } = await api.post(`${BASE_URL}/auth/forgot-password`, body, {
      headers: getHeaders(),
    });
    return data;
  } catch (err: any) {
    return rejectWithValue(err);
  }
});

export const resetPassword = createAsyncThunk<
  ResetPasswordResponse,
  ResetPasswordRequest
>('auth/resetPassword', async (body, { rejectWithValue }) => {
  try {
    const { data } = await api.post(`${BASE_URL}/auth/reset-password`, body, {
      headers: getHeaders(),
    });
    return data;
  } catch (err: any) {
    return rejectWithValue(err);
  }
});

export const getProfile = createAsyncThunk<
  { message: string; data: User },
  void
>('auth/getProfile', async (_, { rejectWithValue }) => {
  try {
    const { data } = await api.get(`${BASE_URL}/auth/user-details`, {
      headers: getHeaders(false),
      withCredentials: true,
    });
    return data;
  } catch (err: any) {
    return rejectWithValue(err);
  }
});

export const getUserById = createAsyncThunk<User, string>(
  'auth/getUserById',
  async (id, { rejectWithValue }) => {
    try {
      const { data } = await api.get(`${BASE_URL}/users/${id}`, {
        headers: getHeaders(false),
        withCredentials: true,
      });
      return data.data;
    } catch (err: any) {
      return rejectWithValue(err);
    }
  }
);

export const logout = createAsyncThunk<{ message: string }, void>(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      const { data } = await api.post(`${BASE_URL}/auth/logout`, {}, {
        headers: getHeaders(false),
        withCredentials: true,
      });
      return data;
    } catch (err: any) {
      return rejectWithValue(err);
    }
  }
);

interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  isLoading: false,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearAuthError(state) {
      state.error = null;
    },
    setUser(state, action: PayloadAction<User | null>) {
      state.user = action.payload;
    },
    clearAuthState(state) {
      state.user = null;
      state.isLoading = false;
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(login.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, state => {
        state.isLoading = false;
        // Do not set user here! User will be set from getProfile.fulfilled
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(register.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, state => {
        state.isLoading = false;
        // Do not set user here! User will be set from getProfile.fulfilled
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(forgotPassword.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(forgotPassword.fulfilled, state => {
        state.isLoading = false;
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(resetPassword.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resetPassword.fulfilled, state => {
        state.isLoading = false;
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(getProfile.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.data;
      })
      .addCase(getProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(getUserById.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getUserById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
      })
      .addCase(getUserById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(googleSignup.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(googleSignup.fulfilled, state => {
        state.isLoading = false;
        // Do not set user here! User will be set from getProfile.fulfilled
      })
      .addCase(googleSignup.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(googleSignin.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(googleSignin.fulfilled, state => {
        state.isLoading = false;
        // Do not set user here! User will be set from getProfile.fulfilled
      })
      .addCase(googleSignin.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(logout.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(logout.fulfilled, state => {
        state.isLoading = false;
        state.user = null;
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearAuthError, setUser, clearAuthState } = authSlice.actions;
export default authSlice.reducer;
