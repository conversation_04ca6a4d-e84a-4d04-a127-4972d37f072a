export interface Education {
  institution: string;
  degree?: string | undefined;
  fieldOfStudy?: string | undefined;
  startYear?: string | undefined;
  endYear?: string | undefined;
  grade?: string | undefined;
  country?: string | undefined;
}

export interface WorkExperience {
  company: string;
  title?: string | undefined;
  location?: string | undefined;
  from_date?: string | undefined; // ISO date string
  to_date?: string | undefined; // ISO date string
  currentlyWorking?: boolean | undefined;
  description?: string | undefined;
}

export interface Certification {
  name: string;
  authority?: string | undefined;
  licenseNumber?: string | undefined;
  year?: string | undefined;
}

export interface StaticProfile {
  technical_skills?: number;
  soft_skills?: number;
  experience?: number;
  cultural_fit?: number;
  total_static_score?: number;
}

export interface CandidateProfile {
  _id?: string;
  userId: string;
  fullName: string;
  email: string;
  phoneNumber?: string;
  currentJobTitle?: string;
  permanentAddress?: string;
  country?: string;
  city?: string;
  postalCode?: string;
  bio?: string;
  educations: Education[];
  workExperience: WorkExperience[];
  skills: string[];
  certifications: Certification[];
  image?: string;
  resume?: string;
  static_profile: StaticProfile;
  createdAt?: string;
  updatedAt?: string;
}

export type UpdateCandidateProfileRequest = Partial<CandidateProfile>;
