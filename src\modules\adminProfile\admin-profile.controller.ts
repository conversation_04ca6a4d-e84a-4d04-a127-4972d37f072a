import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  UseInterceptors,
  UploadedFile,
  UseGuards,
} from '@nestjs/common';
import { AdminProfileService } from './admin-profile.service';
import { CreateAdminProfileDto } from './dto/create-admin-profile.dto';
import { UpdateAdminProfileDto } from './dto/update-admin-profile.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { profileImageMulterOptions } from '../../middlewares/multer.local-disk';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ApiResponse } from '../../response.interceptor';

@UseGuards(JwtAuthGuard)
@Controller('admin-profile')
export class AdminProfileController {
  constructor(private readonly adminProfileService: AdminProfileService) {}

  @Post()
  @UseInterceptors(FileInterceptor('image', profileImageMulterOptions))
  async create(
    @Body() createAdminProfileDto: CreateAdminProfileDto,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<ApiResponse<any>> {
    return this.adminProfileService.createAdminProfile(
      createAdminProfileDto,
      file,
    );
  }

  @Post('full')
  @UseInterceptors(FileInterceptor('image', profileImageMulterOptions))
  async createFull(
    @Body() body: CreateAdminProfileDto,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<ApiResponse<any>> {
    return this.adminProfileService.createFullAdminProfile(body, file);
  }

  @Get()
  async findAll(): Promise<ApiResponse<any>> {
    return this.adminProfileService.findAllProfiles();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<ApiResponse<any>> {
    return this.adminProfileService.findProfileById(id);
  }

  @Patch(':id')
  @UseInterceptors(FileInterceptor('image', profileImageMulterOptions))
  async update(
    @Param('id') id: string,
    @Body() updateAdminProfileDto: UpdateAdminProfileDto,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<ApiResponse<any>> {
    return this.adminProfileService.updateAdminProfile(
      id,
      updateAdminProfileDto,
      file,
    );
  }

  @Patch('full/:userId')
  @UseInterceptors(FileInterceptor('image', profileImageMulterOptions))
  async updateFull(
    @Param('userId') userId: string,
    @Body() body: UpdateAdminProfileDto,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<ApiResponse<any>> {
    return this.adminProfileService.updateFullAdminProfile(userId, body, file);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<ApiResponse<any>> {
    return this.adminProfileService.removeProfile(id);
  }

  @Get('user/:userId')
  async getAdminUserDetails(
    @Param('userId') userId: string,
  ): Promise<ApiResponse<any>> {
    return this.adminProfileService.getAdminUserDetails(userId);
  }
}
