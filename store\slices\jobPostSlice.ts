import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type {
  GenerateJobDescriptionRequest,
  GenerateJobDescriptionResponse,
  JobPostExtractState,
  JobPostExtractData,
} from '@/types/jobPost';

// Extend the state to store both extracted and generated data
interface CombinedJobPostState extends JobPostExtractState {
  generatedData: JobPostExtractData | null;
  extractLoading: boolean;
  generateLoading: boolean;
}

const initialState: CombinedJobPostState = {
  loading: false, // deprecated, keep for compatibility
  error: null,
  extractedData: null,
  generatedData: null,
  extractLoading: false,
  generateLoading: false,
};

const AI_API_BASE_URL =
  process.env.NEXT_PUBLIC_AI_API_BASE_URL || 'http://127.0.0.1:8005';

export const extractJobPost = createAsyncThunk<any, File>(
  'jobPost/extractJobPost',
  async (file, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const res = await fetch(`${AI_API_BASE_URL}/extract_job_description`, {
        method: 'POST',
        body: formData,
      });
      const json = await res.json();
      if (!res.ok || json.status !== 'success')
        throw new Error(json.message || 'Extraction failed');
      return json.data.generated;
    } catch (err: any) {
      return rejectWithValue(err.message);
    }
  }
);

export const generateJobDescription = createAsyncThunk<
  GenerateJobDescriptionResponse,
  GenerateJobDescriptionRequest
>('employee/generateJobDescription', async ({ title }, { rejectWithValue }) => {
  try {
    const res = await fetch(`${AI_API_BASE_URL}/generate_job_description`, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({ title }),
    });
    const json = await res.json();
    if (!res.ok) throw new Error(json.message || 'Generate description failed');
    return json;
  } catch (err: any) {
    return rejectWithValue(err.message);
  }
});

// Helper to merge two job post data objects
function mergeJobPostData(
  extracted: JobPostExtractData | null,
  generated: JobPostExtractData | null
): JobPostExtractData | null {
  if (!extracted && !generated) return null;
  if (!extracted) return generated;
  if (!generated) return extracted;
  return {
    title: generated.title || extracted.title,
    description: generated.description || extracted.description,
    domain: generated.domain || extracted.domain,
    skills: Array.from(
      new Set([...(extracted.skills || []), ...(generated.skills || [])])
    ),
    culture_fit: generated.culture_fit || extracted.culture_fit,
    leadership: generated.leadership || extracted.leadership,
  };
}

const jobPostSlice = createSlice({
  name: 'jobPost',
  initialState,
  reducers: {
    clearExtractedData(state) {
      state.extractedData = null;
      state.error = null;
    },
    clearGeneratedData(state) {
      state.generatedData = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(extractJobPost.pending, state => {
        state.extractLoading = true;
        state.error = null;
      })
      .addCase(extractJobPost.fulfilled, (state, action) => {
        state.extractLoading = false;
        state.extractedData = action.payload;
      })
      .addCase(extractJobPost.rejected, (state, action) => {
        state.extractLoading = false;
        state.error = action.payload as string;
      })
      .addCase(generateJobDescription.pending, state => {
        state.generateLoading = true;
        state.error = null;
      })
      .addCase(generateJobDescription.fulfilled, (state, action) => {
        state.generateLoading = false;
        state.generatedData = action.payload?.data?.generated || null;
      })
      .addCase(generateJobDescription.rejected, (state, action) => {
        state.generateLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Selector to get the merged job post data
export const selectMergedJobPostData = (state: CombinedJobPostState) =>
  mergeJobPostData(state.extractedData, state.generatedData);

export const {
  clearExtractedData: clearJobPostExtractedData,
  clearGeneratedData: clearJobPostGeneratedData,
} = jobPostSlice.actions;
export default jobPostSlice.reducer;
