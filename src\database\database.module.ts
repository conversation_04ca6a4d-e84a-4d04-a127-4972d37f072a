import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { User, UserSchema } from '../modules/user/schemas/user.schema';
import { Role, RoleSchema } from 'src/modules/role/schemas/role.schema';
import { ConfigModule } from '../config/config.module';

@Module({
  imports: [
    ConfigModule, // Ensure ConfigModule is imported globally
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        uri: configService.get<string>('database.mongodb.uri'),
        dbName: configService.get<string>('database.mongodb.dbName'),
        maxPoolSize: configService.get<number>('database.mongodb.poolSize'),
        serverSelectionTimeoutMS: configService.get<number>(
          'database.mongodb.timeoutMS',
        ),
        socketTimeoutMS: configService.get<number>(
          'database.mongodb.socketTimeoutMS',
        ),
        retryAttempts: configService.get<number>(
          'database.mongodb.retryAttempts',
        ),
        retryDelay: configService.get<number>('database.mongodb.retryDelay'),
        autoIndex: configService.get<boolean>('database.mongodb.autoIndex'),
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Role.name, schema: RoleSchema },
    ]),
  ],
  exports: [MongooseModule], // Export MongooseModule for other modules to use
})
export class DatabaseModule {}
