import React, { useRef, useState } from 'react';
import { Avatar, AvatarImage, AvatarFallback } from '../ui/avatar';
import { FaCamera, FaTrash } from 'react-icons/fa';
import <PERSON><PERSON><PERSON> from 'react-easy-crop';
import { Dialog, DialogContent } from '../ui/dialog';

interface AdvancedAvatarUploadProps {
  imageUrl?: string | null;
  onImageChange: (file: File, croppedDataUrl?: string) => void;
  onImageRemove: () => void;
  loading?: boolean;
}

const MAX_SIZE_MB = 2;
const ACCEPTED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];

const AdvancedAvatarUpload: React.FC<AdvancedAvatarUploadProps> = ({
  imageUrl,
  onImageChange,
  onImageRemove,
  loading,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [showCrop, setShowCrop] = useState(false);
  const [cropImage, setCropImage] = useState<string | null>(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFile = (file: File) => {
    if (!ACCEPTED_TYPES.includes(file.type)) {
      setError('Only JPG, PNG, and WEBP images are allowed.');
      return;
    }
    if (file.size > MAX_SIZE_MB * 1024 * 1024) {
      setError('Image must be less than 2MB.');
      return;
    }
    setError(null);
    const reader = new FileReader();
    reader.onload = e => {
      setCropImage(e.target?.result as string);
      setShowCrop(true);
    };
    reader.readAsDataURL(file);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
  };

  const onCropComplete = (_: any, croppedAreaPixels: any) => {
    setCroppedAreaPixels(croppedAreaPixels);
  };

  const getCroppedImg = async (): Promise<string | null> => {
    if (!cropImage || !croppedAreaPixels) return null;
    const { x, y, width, height } = croppedAreaPixels || {};
    if (
      x === undefined ||
      y === undefined ||
      width === undefined ||
      height === undefined
    )
      return null;
    const image = new window.Image();
    image.src = cropImage;
    await new Promise(resolve => (image.onload = resolve));
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    if (!ctx) return null;
    ctx.drawImage(image, x, y, width, height, 0, 0, width, height);
    return canvas.toDataURL('image/jpeg');
  };

  const handleCropSave = async () => {
    if (!cropImage) return;
    const croppedDataUrl = await getCroppedImg();
    if (croppedDataUrl) {
      // Convert dataURL to File
      const arr = croppedDataUrl.split(',');
      if (!arr[0]) return;
      const mimeMatch = arr[0].match(/:(.*?);/);
      if (!mimeMatch || !mimeMatch[1] || !arr[1]) return;
      const mime = mimeMatch[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      const file = new File([u8arr], 'avatar.jpg', { type: mime });
      onImageChange(file, croppedDataUrl);
    }
    setShowCrop(false);
    setCropImage(null);
  };

  return (
    <div className="flex flex-col items-center">
      <div
        className={`relative group w-24 h-24 rounded-full border-2 border-blue-300 shadow-lg overflow-hidden cursor-pointer ${dragActive ? 'ring-4 ring-blue-400' : ''}`}
        onClick={() => inputRef.current?.click()}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        tabIndex={0}
        aria-label="Upload profile image"
      >
        <Avatar className="w-24 h-24">
          {imageUrl ? (
            <AvatarImage
              src={imageUrl}
              alt="Profile"
              className="object-cover"
            />
          ) : (
            <AvatarFallback>U</AvatarFallback>
          )}
        </Avatar>
        <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
          <FaCamera className="text-white text-2xl" />
        </div>
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-60">
            <div className="loader border-blue-500" />
          </div>
        )}
        <input
          ref={inputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleInputChange}
        />
      </div>
      <button
        type="button"
        className="mt-2 text-red-500 hover:text-red-700 flex items-center gap-1 text-sm"
        onClick={onImageRemove}
        aria-label="Remove profile image"
      >
        <FaTrash /> Remove
      </button>
      {error && <div className="text-red-600 text-xs mt-1">{error}</div>}
      <Dialog open={showCrop} onOpenChange={setShowCrop}>
        <DialogContent>
          <div className="p-4 bg-white rounded shadow-lg max-w-xs mx-auto">
            <div className="relative w-64 h-64 bg-gray-100">
              {cropImage && (
                <Cropper
                  image={cropImage}
                  crop={crop}
                  zoom={zoom}
                  aspect={1}
                  onCropChange={setCrop}
                  onZoomChange={setZoom}
                  onCropComplete={onCropComplete}
                />
              )}
            </div>
            <div className="flex justify-between mt-4">
              <button
                className="bg-gray-200 px-4 py-2 rounded"
                onClick={() => setShowCrop(false)}
                type="button"
              >
                Cancel
              </button>
              <button
                className="bg-blue-600 text-white px-4 py-2 rounded"
                onClick={handleCropSave}
                type="button"
              >
                Save
              </button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdvancedAvatarUpload;
