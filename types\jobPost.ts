export interface JobPostExtractData {
  title?: string | undefined;
  description?: string | undefined;
  domain?: string | undefined;
  skills?: string[] | undefined;
  culture_fit?: string | undefined;
  leadership?: string | undefined;
}

export interface JobPostExtractState {
  loading: boolean;
  error: string | null;
  extractedData: JobPostExtractData | null;
}

export interface GenerateJobDescriptionRequest {
  title: string;
}

export interface GenerateJobDescriptionResponse {
  // Define the response structure as needed, using 'any' for now
  [key: string]: any;
}
