import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, ShieldCheck } from 'lucide-react';

interface CompletionSidebarProps {
  completionData: unknown;
  animatedPercent: number;
  checklist?: { label: string; value: string; done: boolean }[];
}

const defaultChecklist = [
  { label: 'Setup Account', value: '+20%', done: true },
  { label: 'Personal Information', value: '+20%', done: true },
  { label: 'Skills & technology', value: '+10%', done: true },
  { label: 'Work Experience', value: '+30%', done: true },
  { label: 'Certifications', value: '+20%', done: false },
  { label: 'Educations', value: '+10%', done: false },
];

function hasCompletion(obj: unknown): obj is { completion: unknown } {
  return Boolean(obj && typeof obj === 'object' && 'completion' in obj);
}

const CompletionSidebar: React.FC<CompletionSidebarProps> = ({
  completionData,
  animatedPercent,
  checklist,
}) => {
  const { completionChecklist } = useMemo(() => {
    let completionChecklist = checklist || defaultChecklist;
    if (hasCompletion(completionData)) {
      const completion = (completionData as any).completion as {
        setupAccount: number;
        personalInfo: number;
        skillsAndTechnology: number;
        workExperience: number;
        certifications: number;
        education: number;
      };
      completionChecklist = [
        {
          label: 'Setup Account',
          value: `+${completion.setupAccount}%`,
          done: completion.setupAccount > 0,
        },
        {
          label: 'Personal Information',
          value: `+${completion.personalInfo}%`,
          done: completion.personalInfo > 0,
        },
        {
          label: 'Skills & technology',
          value: `+${completion.skillsAndTechnology}%`,
          done: completion.skillsAndTechnology > 0,
        },
        {
          label: 'Work Experience',
          value: `+${completion.workExperience}%`,
          done: completion.workExperience > 0,
        },
        {
          label: 'Certifications',
          value: `+${completion.certifications}%`,
          done: completion.certifications > 0,
        },
        {
          label: 'Educations',
          value: `+${completion.education}%`,
          done: completion.education > 0,
        },
      ];
    }
    return { completionChecklist };
  }, [completionData, checklist]);

  return (
    <div className="w-full lg:w-80 flex-shrink-0 mt-6 lg:mt-0">
      <div className="rounded-3xl bg-white shadow-lg p-4 md:p-8 mb-6 w-full">
        <div className="text-[#193E6C] font-semibold mb-4 text-center">
          Complete your Profile
        </div>
        {/* Circular Progress */}
        <div className="flex flex-col items-center mb-6">
          <motion.svg
            width="120"
            height="120"
            viewBox="0 0 120 120"
            initial={{ rotate: -90 }}
            animate={{ rotate: 0 }}
            transition={{ duration: 0.6 }}
          >
            <circle
              cx="60"
              cy="60"
              r="54"
              stroke="#E3F0FF"
              strokeWidth="12"
              fill="none"
            />
            <motion.circle
              cx="60"
              cy="60"
              r="54"
              stroke="#3C9DF6"
              strokeWidth="12"
              fill="none"
              strokeDasharray={339.292}
              strokeDashoffset={339.292 * (1 - animatedPercent / 100)}
              strokeLinecap="round"
              initial={{ strokeDashoffset: 339.292 }}
              animate={{
                strokeDashoffset: 339.292 * (1 - animatedPercent / 100),
              }}
              transition={{ duration: 1.2, ease: 'easeInOut' }}
            />
            <motion.text
              x="50%"
              y="54%"
              textAnchor="middle"
              dy="0.3em"
              fontSize="2.2em"
              fill="#0858F8"
              fontWeight="bold"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
            >
              {animatedPercent}%
            </motion.text>
          </motion.svg>
        </div>
        <div className="space-y-3">
          {completionChecklist.map((item, idx) => (
            <motion.div
              key={item.label}
              className="flex items-center gap-2 text-sm"
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 + idx * 0.1 }}
            >
              <motion.span
                initial={
                  item.done
                    ? { scale: 0.5, opacity: 0.5 }
                    : { scale: 1, opacity: 0.5 }
                }
                animate={
                  item.done
                    ? { scale: 1.2, opacity: 1 }
                    : { scale: 1, opacity: 0.5 }
                }
                transition={{ type: 'spring', stiffness: 300, damping: 15 }}
              >
                <CheckCircle
                  className={`w-4 h-4 ${item.done ? 'text-[#3C9DF6]' : 'text-gray-300'}`}
                />
              </motion.span>
              <span className={item.done ? 'text-[#193E6C]' : 'text-gray-400'}>
                {item.label}
              </span>
              <span className="ml-auto font-semibold text-xs text-[#3C9DF6]">
                {item.value}
              </span>
            </motion.div>
          ))}
        </div>
      </div>
      <div className="rounded-3xl bg-[#3C9DF6] text-white shadow-lg p-4 md:p-6 flex items-center gap-4 w-full">
        <ShieldCheck className="w-10 h-10 text-white" />
        <div>
          <div className="font-bold text-lg">Blockchain Verified Profile</div>
          <div className="text-xs mt-1">
            Your profile is secured with blockchain technology for authenticity
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompletionSidebar;
