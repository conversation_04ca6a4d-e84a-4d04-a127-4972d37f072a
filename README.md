# Next.js + TypeScript Frontend

A production-ready Next.js application with TypeScript, Redux Toolkit, and modern development tools.

## 🚀 Features

- **Next.js 14** with App Router
- **TypeScript** with strict mode configuration
- **Redux Toolkit** for state management
- **Tailwind CSS** for styling
- **shadcn/ui** component library
- **ESLint + Prettier** for code quality
- **pnpm** for fast package management
- **Environment variable** management

## 🛠️ Tech Stack

- **Framework:** Next.js 14
- **Language:** TypeScript
- **State Management:** Redux Toolkit
- **Styling:** Tailwind CSS
- **Components:** shadcn/ui + Radix UI
- **Linting:** ESLint + Prettier
- **Package Manager:** pnpm

## 📦 Installation

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start
```

## 🔧 Development Scripts

```bash
# Development
pnpm dev              # Start development server
pnpm build            # Build for production
pnpm start            # Start production server

# Code Quality
pnpm lint             # Run ESLint
pnpm lint:fix         # Fix ESLint issues
pnpm format           # Format with Prettier
pnpm format:check     # Check Prettier formatting
pnpm type-check       # Run TypeScript type checking

# Utilities
pnpm clean            # Clean build artifacts
pnpm analyze          # Analyze bundle size
```

## 📁 Project Structure

```
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   └── ui/               # shadcn/ui components
├── store/                # Redux store
│   ├── slices/           # Redux slices
│   ├── index.ts          # Store configuration
│   └── providers.tsx     # Redux provider
├── lib/                  # Utility functions
├── hooks/                # Custom hooks
└── types/                # TypeScript type definitions
```

## 🔧 Configuration Files

- **TypeScript:** `tsconfig.json` - Strict mode enabled
- **ESLint:** `.eslintrc.json` - Next.js + TypeScript + Prettier
- **Prettier:** `.prettierrc` - Code formatting rules
- **Tailwind:** `tailwind.config.ts` - Tailwind configuration
- **Next.js:** `next.config.js` - Next.js configuration

## 🌍 Environment Variables

Copy `.env.local.example` to `.env.local` and fill in your values:

```bash
cp .env.local.example .env.local
```

## 🚀 Deployment

This project is configured for static export and can be deployed to any static hosting service:

```bash
pnpm build
```

The `out/` directory will contain the static files ready for deployment.

## 📝 Development Guidelines

### TypeScript

- Strict mode is enabled for better type safety
- Use proper typing for all functions and components
- Avoid `any` type - use proper types or `unknown`

### State Management

- Use Redux Toolkit for complex state
- Use React's built-in state for component-specific state
- Follow the slice pattern for organizing state

### Styling

- Use Tailwind CSS utility classes
- Use shadcn/ui components for consistent UI
- Follow the design system for spacing and colors

### Code Quality

- Run `pnpm lint:fix` before committing
- Use `pnpm format` to ensure consistent formatting
- Write meaningful commit messages

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
