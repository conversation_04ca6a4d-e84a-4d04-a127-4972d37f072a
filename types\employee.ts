// Employee API types
export interface RequestVerificationRequest {
  email: string;
}
export interface RequestVerificationResponse {
  message: string;
}
export interface VerifyDomainRequest {
  token: string;
}
export interface VerifyDomainResponse {
  message: string;
}
export interface CreateJobRequest {
  // Define the job fields as needed, using 'any' for now
  [key: string]: any;
}
export interface CreateJobResponse {
  message: string;
  data: any;
}
export interface GetEmployerJobsResponse {
  message: string;
  data: any[];
}
export interface GetDomainVerificationStatusResponse {
  domainVerified: boolean;
}
export interface GenerateJobDescriptionRequest {
  title: string;
}
export interface GenerateJobDescriptionResponse {
  // Define the response structure as needed, using 'any' for now
  [key: string]: any;
}
export interface Job {
  _id?: string;
  id?: string;
  title: string;
  company: string;
  location: string;
  remote_ok: boolean;
  salary_min: number;
  salary_max: number;
  description: string;
  domain: string;
  criteria_weights: {
    technical_skills: number;
    soft_skills: number;
    experience: number;
    cultural_fit: number;
  };
  [key: string]: any;
}
