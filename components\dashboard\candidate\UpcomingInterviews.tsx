import React from 'react';

interface Interview {
  role: string;
  company: string;
  location: string;
  time: string;
}

export default function UpcomingInterviews({
  interviews,
}: {
  interviews: Interview[];
}) {
  return (
    <div className="bg-white rounded-2xl shadow p-6">
      <h2 className="font-semibold text-lg mb-4">Upcoming Interviews</h2>
      <ul className="space-y-4">
        {interviews.map((interview, i) => (
          <li key={i} className="flex items-center gap-3">
            <span className="bg-[#FFF7F0] rounded-full w-12 h-12 flex items-center justify-center text-2xl">
              ✉️
            </span>
            <div>
              <div className="font-medium">{interview.role}</div>
              <div className="text-xs text-gray-500">
                {interview.company} - {interview.location}
              </div>
              <div className="text-xs text-gray-400">{interview.time}</div>
              <span className="inline-block bg-[#E0E7FF] text-[#377DFF] rounded-full px-3 py-1 text-xs mt-1">
                Full-Time
              </span>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
