import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface Stat {
  label: string;
  value: string;
  icon: React.ReactNode;
}

export default function TopBanner({
  user,
  profileCompletion,
  stats,
}: {
  user: unknown;
  profileCompletion: number;
  stats: Stat[];
}) {
  const typedUser = user as any;
  // Animated percent state
  const [animatedPercent, setAnimatedPercent] = useState(0);
  useEffect(() => {
    const duration = 1.2; // seconds
    const startTime = performance.now();
    function animate(now: number) {
      const elapsed = (now - startTime) / 1000;
      const progress = Math.min(elapsed / duration, 1);
      setAnimatedPercent(Math.floor(progress * profileCompletion));
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        setAnimatedPercent(profileCompletion);
      }
    }
    requestAnimationFrame(animate);
  }, [profileCompletion]);

  return (
    <div className="bg-[#377DFF] rounded-b-3xl px-8 py-8 text-white relative">
      <div className="max-w-6xl mx-auto flex flex-col gap-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-1">
              Welcome Back {typedUser.firstName}!
            </h1>
            <p className="text-lg opacity-80">
              Here is what is happening in your job search
            </p>
          </div>
          <div className="flex flex-col items-end mt-4 md:mt-0">
            <span className="text-sm mb-1">Profile Completed</span>
            <div className="w-48 h-2 bg-white/30 rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-white"
                initial={{ width: 0 }}
                animate={{ width: `${animatedPercent}%` }}
                transition={{ duration: 1.2, ease: 'easeInOut' }}
                style={{ minWidth: 0 }}
              />
            </div>
            <motion.span
              className="text-sm font-semibold mt-1"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
            >
              {animatedPercent}%
            </motion.span>
          </div>
        </div>
        {/* Stats */}
        <div className="flex gap-6 mt-6">
          {stats.map((stat, i) => (
            <div
              key={i}
              className="flex-1 bg-white/90 rounded-2xl shadow p-6 flex flex-col items-center text-[#333] min-w-[160px]"
            >
              <span className="text-2xl mb-2">{stat.value}</span>
              <span className="flex items-center gap-2 text-sm font-medium opacity-80">
                <span className="inline-block bg-[#F5EFFF] text-[#377DFF] rounded-full w-8 h-8 flex items-center justify-center">
                  {stat.icon}
                </span>
                {stat.label}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
