'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Minus, Plus, RotateCcw } from 'lucide-react';

interface CounterDemoProps {
  value: number;
  loading: boolean;
  onIncrement: () => void;
  onDecrement: () => void;
  onReset: () => void;
}

export function CounterDemo({
  value,
  loading,
  onIncrement,
  onDecrement,
  onReset,
}: CounterDemoProps) {
  return (
    <Card className="max-w-md mx-auto border-0 shadow-xl mb-12">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          Redux Counter Demo
          <Badge variant="secondary">Live</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="text-center">
          <div className="text-6xl font-bold text-slate-800 dark:text-slate-200 mb-2">
            {value}
          </div>
          <p className="text-slate-500 dark:text-slate-400">Current Count</p>
        </div>

        <div className="flex gap-2 justify-center">
          <Button
            variant="outline"
            size="icon"
            onClick={onDecrement}
            disabled={loading}
            className="h-12 w-12"
          >
            <Minus className="h-4 w-4" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            onClick={onReset}
            disabled={loading}
            className="h-12 w-12"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>

          <Button
            size="icon"
            onClick={onIncrement}
            disabled={loading}
            className="h-12 w-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        <div className="text-center text-sm text-slate-500 dark:text-slate-400">
          State managed with Redux Toolkit
        </div>
      </CardContent>
    </Card>
  );
}
