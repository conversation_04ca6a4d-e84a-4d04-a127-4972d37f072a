import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default:
          'bg-[#0858F8] text-white hover:bg-[#1e40af] border border-[var(--button-border)]',
        destructive: 'bg-red-600 text-white hover:bg-red-700',
        outline:
          'border border-[var(--button-border)] bg-[#0858F8] text-white hover:bg-[#1e40af]',
        secondary:
          'bg-[var(--bg-gradient-1)] text-[var(--primary-text)] hover:bg-[var(--bg-gradient-2)]',
        ghost: 'hover:bg-[var(--bg-gradient-4)] text-[var(--primary-text)]',
        link: 'text-[var(--primary-text)] underline-offset-4 hover:underline',
        gradient:
          'bg-[#0858F8] border border-[var(--button-border)] text-white hover:bg-[#1e40af]',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
