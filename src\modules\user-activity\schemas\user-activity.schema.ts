import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type UserActivityDocument = UserActivity & Document;

@Schema({
  collection: 'user_activity',
  timestamps: { createdAt: true, updatedAt: true },
})
export class UserActivity {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ required: true })
  eventName: string;

  @Prop()
  endpoint: string;

  @Prop()
  screenName: string;

  @Prop()
  slug: string;

  @Prop()
  description: string;

  @Prop()
  ipAddress: string;

  @Prop()
  osName: string;

  @Prop()
  browser: string;

  @Prop()
  source: string;

  @Prop()
  origin: string;

  @Prop()
  osVersion: string;

  @Prop()
  agentVersion: string;

  @Prop({ type: Object, default: {} })
  request: Record<string, unknown>;
}

export const UserActivitySchema = SchemaFactory.createForClass(UserActivity);

// Optional index for performance
UserActivitySchema.index({ userId: 1, eventName: 1, createdAt: -1 });
