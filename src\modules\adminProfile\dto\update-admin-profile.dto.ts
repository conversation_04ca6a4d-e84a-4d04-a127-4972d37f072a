import {
  IsMongoId,
  IsOptional,
  IsString,
  IsEmail,
  IsBoolean,
} from 'class-validator';

export class UpdateAdminProfileDto {
  // User updatable fields
  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsMongoId()
  role?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  // AdminProfile fields
  @IsOptional()
  @IsMongoId()
  userId?: string;

  @IsOptional()
  @IsString()
  image?: string;
}
