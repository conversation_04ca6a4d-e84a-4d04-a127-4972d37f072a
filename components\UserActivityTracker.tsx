'use client';

import { useEffect, useRef } from 'react';
import { useUserActivity } from '../hooks/useUserActivity';
import { useAppSelector } from '../store';

interface UserActivityTrackerProps {
  pageName: string;
  children: React.ReactNode;
}

export const UserActivityTracker: React.FC<UserActivityTrackerProps> = ({
  pageName,
  children,
}) => {
  const { trackPageView } = useUserActivity();
  const profileData = useAppSelector(state => state.auth.user);
  const hasTracked = useRef(false);

  useEffect(() => {
    // Only track if user is authenticated
    if (profileData && !hasTracked.current) {
      trackPageView(pageName);
      hasTracked.current = true;
    }
  }, [pageName, trackPageView, profileData]);

  return <>{children}</>;
};

// Higher-order component for easier usage
export const withUserActivityTracking = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  pageName: string
) => {
  const WithUserActivityTracking = (props: P) => (
    <UserActivityTracker pageName={pageName}>
      <WrappedComponent {...props} />
    </UserActivityTracker>
  );

  WithUserActivityTracking.displayName = `withUserActivityTracking(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithUserActivityTracking;
};
