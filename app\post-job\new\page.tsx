'use client';
import { JobForm } from '@/components/job/job-form';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import { useAppDispatch, useAppSelector } from '@/store';
import { createJob } from '@/store/slices/employeeSlice';
import type { CreateJobRequest } from '@/types/employee';

export default function NewJobPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(state => state.employee.loading);
  const error = useAppSelector(state => state.employee.error);

  const handleSubmit = async (data: CreateJobRequest) => {
    try {
      await dispatch(createJob(data)).unwrap();
      toast.success('Job posted successfully!', { duration: 4000 });
      router.push('/post-job');
    } catch (err: unknown) {
      const msg =
        (err as any).data?.message ||
        (err as any).message ||
        'Failed to create job';
      toast.error(msg, { duration: 4000 });
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-[#E3F0FF] to-[#F6F9FF] py-10">
      <div className="w-3/4 max-w-4xl bg-white rounded-3xl shadow-lg p-8">
        <h1 className="text-2xl font-bold mb-6">Post a New Job</h1>
        <JobForm onSubmit={handleSubmit} isLoading={isLoading} />
        {error && <div className="text-red-600 mt-2">{error}</div>}
      </div>
    </div>
  );
}
