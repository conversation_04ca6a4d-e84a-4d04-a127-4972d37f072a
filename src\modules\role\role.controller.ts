import { Controller, Get, UseGuards, Param } from '@nestjs/common';
import { RoleService } from './role.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@Controller('roles')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Get()
  async listRolesExceptAdmin() {
    return this.roleService.findAllExceptAdmin();
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  async getRoleById(@Param('id') id: string) {
    const role = await this.roleService.findById(id);
    if (!role) {
      return { message: 'Role not found', data: null };
    }
    return { message: 'Role fetched successfully', data: { name: role.name } };
  }
}
