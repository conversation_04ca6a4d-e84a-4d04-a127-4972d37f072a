import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  AdminProfile,
  AdminProfileDocument,
} from './schemas/admin-profile.schema';
import { CreateAdminProfileDto } from './dto/create-admin-profile.dto';
import { UpdateAdminProfileDto } from './dto/update-admin-profile.dto';
import { ApiResponse } from '../../response.interceptor';
import { User } from '../user/schemas/user.schema';
import { Role } from '../role/schemas/role.schema';
import * as bcrypt from 'bcrypt';
import { ERROR } from '../../utils/error-code';
import { ConfigService } from '@nestjs/config';
import { ROLE } from 'src/utils/constants';

@Injectable()
export class AdminProfileService {
  constructor(
    @InjectModel(AdminProfile.name)
    private adminProfileModel: Model<AdminProfileDocument>,
    @InjectModel('User') private readonly userModel: Model<User>,
    @InjectModel('Role') private readonly roleModel: Model<Role>,
    private readonly configService: ConfigService,
  ) {}

  async createAdminProfile(
    createAdminProfileDto: CreateAdminProfileDto,
    file?: Express.Multer.File,
  ): Promise<ApiResponse<any>> {
    try {
      if (file) {
        createAdminProfileDto.image = file.filename;
      }
      const created = new this.adminProfileModel(createAdminProfileDto);
      const profile = await created.save();
      const profileObj = profile ? JSON.parse(JSON.stringify(profile)) : null;
      return {
        statusCode: 201,
        message: 'Admin profile created',
        data: {
          ...profileObj,
          image: file
            ? `${this.configService.get('app.backendUrl')}/uploads/profile-image/${file.filename}`
            : null,
        },
      };
    } catch (error) {
      return {
        statusCode: 500,
        message: error.message || ERROR.INTERNAL_SERVER_ERROR,
        data: null,
      };
    }
  }

  async createFullAdminProfile(
    body: CreateAdminProfileDto,
    file?: Express.Multer.File,
  ): Promise<ApiResponse<any>> {
    try {
      const { firstName, lastName, email, password, isActive } = body;

      // 1. Check for existing user
      const existingUser = await this.userModel.findOne({ email });
      if (existingUser) {
        return {
          statusCode: 400,
          message: ERROR.USER_EXISTS,
          data: null,
        };
      }

      // 2. Get Admin Role
      const adminRole = await this.roleModel.findOne({ name: ROLE.ADMIN });
      if (!adminRole) {
        return {
          statusCode: 400,
          message: ERROR.ADMIN_ROLE_NOT_FOUND,
          data: null,
        };
      }

      // 3. Create User with hashed password
      const hashedPassword = password
        ? await bcrypt.hash(password, 10)
        : undefined;

      const user: User = await this.userModel.create({
        firstName,
        lastName,
        email,
        password: hashedPassword,
        role: adminRole._id,
        isActive,
      });

      // 4. Create Admin Profile
      const adminProfileData: { userId: string; image?: string } = {
        userId: (user._id as Types.ObjectId).toString(),
      };
      if (file?.filename) {
        adminProfileData.image = file.filename;
      }

      const adminProfile =
        await this.adminProfileModel.create(adminProfileData);

      // 5. Construct Full Image URL
      const imageUrl = file
        ? `${this.configService.get('app.backendUrl')}/uploads/profile-image/${file.filename}`
        : null;

      return {
        statusCode: 201,
        message: ERROR.ADMIN_USER_PROFILE_CREATED,
        data: {
          user: user.toObject(),
          adminProfile: {
            ...adminProfile.toObject(),
            image: imageUrl,
          },
        },
      };
    } catch (error) {
      console.error('Admin profile creation error:', error);
      return {
        statusCode: 500,
        message: error.message || ERROR.INTERNAL_SERVER_ERROR,
        data: null,
      };
    }
  }

  async findAllProfiles(): Promise<ApiResponse<any>> {
    try {
      const profiles = await this.adminProfileModel
        .find()
        .populate('userId')
        .exec();
      return {
        statusCode: 200,
        message: 'All admin profiles',
        data: profiles,
      };
    } catch (error) {
      return {
        statusCode: 500,
        message: error.message || ERROR.INTERNAL_SERVER_ERROR,
        data: null,
      };
    }
  }

  async findProfileById(id: string): Promise<ApiResponse<any>> {
    try {
      const profile = await this.adminProfileModel
        .findById(id)
        .populate('userId')
        .exec();
      if (!profile) {
        return {
          statusCode: 404,
          message: 'Admin profile not found',
          data: null,
        };
      }
      return {
        statusCode: 200,
        message: 'Admin profile found',
        data: profile,
      };
    } catch (error) {
      return {
        statusCode: 500,
        message: error.message || ERROR.INTERNAL_SERVER_ERROR,
        data: null,
      };
    }
  }

  async updateAdminProfile(
    id: string,
    updateAdminProfileDto: UpdateAdminProfileDto,
    file?: Express.Multer.File,
  ): Promise<ApiResponse<any>> {
    try {
      if (file) {
        updateAdminProfileDto.image = file.filename;
      }
      const profile = await this.adminProfileModel.findByIdAndUpdate(
        id,
        updateAdminProfileDto,
        { new: true },
      );
      const profileObj = profile ? JSON.parse(JSON.stringify(profile)) : null;
      return {
        statusCode: 200,
        message: 'Admin profile updated',
        data: profileObj
          ? {
              ...profileObj,
              image: file
                ? `${this.configService.get('app.backendUrl')}/uploads/profile-image/${file.filename}`
                : profileObj.image,
            }
          : null,
      };
    } catch (error) {
      return {
        statusCode: 500,
        message: error.message || ERROR.INTERNAL_SERVER_ERROR,
        data: null,
      };
    }
  }

  async updateFullAdminProfile(
    userId: string,
    body: UpdateAdminProfileDto,
    file?: Express.Multer.File,
  ): Promise<ApiResponse<any>> {
    try {
      // 1. Destructure fields and build update object
      const { firstName, lastName, email, password, isActive } = body;

      const userUpdate: Partial<UpdateAdminProfileDto> & { password?: string } =
        {};

      if (firstName) userUpdate.firstName = firstName;
      if (lastName) userUpdate.lastName = lastName;
      if (email) {
        // Check for duplicate email
        const existingUser = await this.userModel.findOne({
          email,
          _id: { $ne: userId },
        });

        if (existingUser) {
          return {
            statusCode: 400,
            message: ERROR.USER_EXISTS,
            data: null,
          };
        }
        userUpdate.email = email;
      }

      if (isActive) userUpdate.isActive = isActive;
      if (password) userUpdate.password = await bcrypt.hash(password, 10);

      // 2. Ensure admin role exists
      const adminRole = await this.roleModel.findOne({ name: ROLE.ADMIN });
      if (!adminRole) {
        return {
          statusCode: 400,
          message: ERROR.ADMIN_ROLE_NOT_FOUND,
          data: null,
        };
      }

      // 3. Update user
      const updatedUser = await this.userModel.findByIdAndUpdate(
        userId,
        userUpdate,
        {
          new: true,
        },
      );

      if (!updatedUser) {
        return {
          statusCode: 404,
          message: ERROR.USER_NOT_FOUND,
          data: null,
        };
      }

      // 4. Update Admin Profile
      const adminProfileUpdate: Partial<{ image: string }> = {};
      if (file?.filename) {
        adminProfileUpdate.image = file.filename;
      }

      let updatedAdminProfile = await this.adminProfileModel.findOneAndUpdate(
        { userId },
        adminProfileUpdate,
        { new: true },
      );

      // Fallback if admin profile doesn't exist
      if (!updatedAdminProfile && file) {
        updatedAdminProfile = await this.adminProfileModel.create({
          userId: String(updatedUser._id as Types.ObjectId),
          image: file.filename,
        });
      }

      // 5. Construct image URL
      const imageUrl = file
        ? `${this.configService.get('app.backendUrl')}/uploads/profile-image/${file.filename}`
        : (updatedAdminProfile?.image ?? null);

      return {
        statusCode: 200,
        message: ERROR.ADMIN_PROFILE_UPDATED,
        data: {
          user: updatedUser.toObject(),
          adminProfile: updatedAdminProfile
            ? {
                ...updatedAdminProfile.toObject(),
                image: imageUrl,
              }
            : null,
        },
      };
    } catch (error) {
      console.error('Update admin profile error:', error);
      return {
        statusCode: 500,
        message: error.message || ERROR.INTERNAL_SERVER_ERROR,
        data: null,
      };
    }
  }

  async removeProfile(id: string): Promise<ApiResponse<any>> {
    try {
      const removed = await this.adminProfileModel.findByIdAndDelete(id).exec();
      if (!removed) {
        return {
          statusCode: 404,
          message: 'Admin profile not found',
          data: null,
        };
      }
      return {
        statusCode: 200,
        message: 'Admin profile deleted',
        data: removed,
      };
    } catch (error) {
      return {
        statusCode: 500,
        message: error.message || ERROR.INTERNAL_SERVER_ERROR,
        data: null,
      };
    }
  }

  async getAdminUserDetails(userId: string): Promise<ApiResponse<any>> {
    try {
      const user = await this.userModel.findById(userId).lean();
      const adminProfile = await this.adminProfileModel.findOne({ userId });
      const adminProfileObj = adminProfile
        ? JSON.parse(JSON.stringify(adminProfile))
        : null;
      if (adminProfileObj && adminProfileObj.image) {
        adminProfileObj.image = `${this.configService.get('app.backendUrl')}/uploads/profile-image/${adminProfileObj.image}`;
      }
      if (!user) {
        return {
          statusCode: 404,
          message: 'Admin user not found',
          data: null,
        };
      }
      return {
        statusCode: 200,
        message: 'Admin user details',
        data: {
          ...user,
          adminProfile: adminProfileObj,
        },
      };
    } catch (error) {
      return {
        statusCode: 500,
        message: error.message || ERROR.INTERNAL_SERVER_ERROR,
        data: null,
      };
    }
  }

  async findAll(): Promise<AdminProfile[]> {
    return this.adminProfileModel.find().populate('userId').exec();
  }

  async findOne(id: string): Promise<AdminProfile | null> {
    return this.adminProfileModel.findById(id).populate('userId').exec();
  }

  async findByUserId(userId: string): Promise<AdminProfile | null> {
    return this.adminProfileModel
      .findOne({ userId: new Types.ObjectId(userId) })
      .exec();
  }

  async update(
    id: string,
    updateAdminProfileDto: UpdateAdminProfileDto,
  ): Promise<AdminProfile | null> {
    return this.adminProfileModel
      .findByIdAndUpdate(id, updateAdminProfileDto, { new: true })
      .exec();
  }

  async remove(id: string): Promise<AdminProfile | null> {
    return this.adminProfileModel.findByIdAndDelete(id).exec();
  }
}
