import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import type { Education } from '@/types/candidateProfile';

interface EducationsTabProps {
  educations: Education[];
  editing: boolean;
  handleArrayAdd: (
    field: 'educations',
    emptyObj: Record<string, unknown>
  ) => void;
  handleArrayRemove: (field: 'educations', idx: number) => void;
  handleArrayChange: (
    field: 'educations',
    idx: number,
    subfield: string,
    value: string | number | boolean
  ) => void;
}

const EducationsTab: React.FC<EducationsTabProps> = ({
  educations,
  editing,
  handleArrayAdd,
  handleArrayRemove,
  handleArrayChange,
}) => {
  const years = Array.from({ length: 60 }, (_, i) =>
    (new Date().getFullYear() - i).toString()
  );
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <div className="font-semibold text-[#193E6C]">Educations</div>
        {editing && (
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              handleArrayAdd('educations', {
                institution: '',
                degree: '',
                fieldOfStudy: '',
                startYear: '',
                endYear: '',
                grade: '',
                country: '',
              })
            }
          >
            <Plus className="w-4 h-4" /> Add
          </Button>
        )}
      </div>
      <div className="space-y-4">
        {(educations ?? []).map((edu, idx) => (
          <div
            key={idx}
            className="bg-white rounded-lg p-4 flex flex-col gap-2 relative"
          >
            {editing && (
              <button
                className="absolute top-2 right-2 text-red-500"
                onClick={() => handleArrayRemove('educations', idx)}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            )}
            <input
              className="input"
              placeholder="Institution"
              value={edu.institution}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange(
                  'educations',
                  idx,
                  'institution',
                  e.target.value
                )
              }
            />
            <input
              className="input"
              placeholder="Degree"
              value={edu.degree}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange('educations', idx, 'degree', e.target.value)
              }
            />
            <input
              className="input"
              placeholder="Field of Study"
              value={edu.fieldOfStudy}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange(
                  'educations',
                  idx,
                  'fieldOfStudy',
                  e.target.value
                )
              }
            />
            <select
              className="input"
              value={edu.startYear}
              disabled={!editing}
              onChange={e =>
                handleArrayChange(
                  'educations',
                  idx,
                  'startYear',
                  e.target.value
                )
              }
            >
              <option value="">Start Year</option>
              {years.map(year => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
            <div className="flex items-center gap-2">
              <select
                className="input"
                value={edu.endYear}
                disabled={!editing}
                onChange={e =>
                  handleArrayChange(
                    'educations',
                    idx,
                    'endYear',
                    e.target.value
                  )
                }
              >
                <option value="">End Year</option>
                {years.map(year => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            </div>
            <input
              className="input"
              placeholder="Grade"
              value={edu.grade}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange('educations', idx, 'grade', e.target.value)
              }
            />
            <input
              className="input"
              placeholder="Country"
              value={edu.country}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange('educations', idx, 'country', e.target.value)
              }
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default EducationsTab;
