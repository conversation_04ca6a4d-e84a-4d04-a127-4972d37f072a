import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { Role, GetRoleByIdResponse, RolesState } from '@/types/roles';

import api from '@/lib/api';

const BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4000';

export const fetchRoles = createAsyncThunk<Role[]>(
  'roles/fetchRoles',
  async (_, { rejectWithValue }) => {
    try {
      const { data } = await api.get(`${BASE_URL}/roles`);
      return data.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchRoleById = createAsyncThunk<GetRoleByIdResponse, string>(
  'roles/fetchRoleById',
  async (id, { rejectWithValue }) => {
    try {
      const { data } = await api.get(`${BASE_URL}/roles/${id}`);
      return data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const initialState: RolesState = {
  roles: [],
  selectedRole: null,
  loading: false,
  error: null,
};

const rolesSlice = createSlice({
  name: 'roles',
  initialState,
  reducers: {
    clearRoles: state => {
      state.roles = [];
      state.selectedRole = null;
      state.loading = false;
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchRoles.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRoles.fulfilled, (state, action) => {
        state.loading = false;
        state.roles = action.payload;
      })
      .addCase(fetchRoles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch roles';
      })
      .addCase(fetchRoleById.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRoleById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedRole = action.payload;
      })
      .addCase(fetchRoleById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch role by id';
      });
  },
});

export const { clearRoles } = rolesSlice.actions;
export default rolesSlice.reducer;
