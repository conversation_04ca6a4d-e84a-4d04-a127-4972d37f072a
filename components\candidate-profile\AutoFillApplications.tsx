import React from 'react';
import { Button } from '@/components/ui/button';
import { User } from 'lucide-react';

interface AutoFillApplicationsProps {
  editing: boolean;
  resumeLoading: boolean;
  onResumeUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const AutoFillApplications: React.FC<AutoFillApplicationsProps> = ({
  editing,
  resumeLoading,
  onResumeUpload,
}) => {
  return (
    <div className="rounded-xl border border-[#E3F0FF] bg-[#F6F9FF] p-4 flex flex-col md:flex-row md:items-center md:justify-between mb-6">
      <div>
        <div className="flex items-center gap-2 text-[#3C9DF6] font-semibold mb-1">
          <User className="w-4 h-4" /> AUTO FILL APPLICATIONS
        </div>
        <div className="text-[#193E6C] text-sm">
          Save your time by importing your latest resume.
        </div>
      </div>
      <div className="mt-2 md:mt-0 flex items-center gap-2">
        <input
          type="file"
          accept="application/pdf"
          id="resume-upload"
          style={{ display: 'none' }}
          onChange={onResumeUpload}
          disabled={!editing || resumeLoading}
        />
        <label htmlFor="resume-upload">
          <Button
            variant="outline"
            size="sm"
            asChild
            disabled={!editing || resumeLoading}
          >
            <span>{resumeLoading ? 'Uploading...' : 'Upload Resume'}</span>
          </Button>
        </label>
      </div>
    </div>
  );
};

export default AutoFillApplications;
