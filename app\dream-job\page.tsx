'use client';
import React, { useState } from 'react';

// Mock data for dropdowns and saved searches
const categories = ['Engineering', 'Design', 'Marketing', 'Product'];
const contractTypes = ['Full-time', 'Part-time', 'Contract'];
const datePostedOptions = [
  'Any time',
  'Past 24 hours',
  'Past week',
  'Past month',
];
const savedSearches = [
  {
    title: 'Senior Project Manager',
    company: 'Nomad',
    location: 'Paris, France',
    type: 'Full-Time',
    salary: '$80k~120k',
  },
  {
    title: 'React Developer',
    company: 'Nomad',
    location: 'Paris, France',
    type: 'Full-Time',
    salary: '$80k~120k',
  },
  {
    title: 'AI Prompt Engineer',
    company: 'Nomad',
    location: 'Paris, France',
    type: 'Full-Time',
    salary: '$80k~120k',
  },
  {
    title: 'Full Stack Engineer',
    company: 'Nomad',
    location: 'Paris, France',
    type: 'Full-Time',
    salary: '$80k~120k',
  },
];

export default function DreamJobPage() {
  const [skills, setSkills] = useState(['Python', 'Angular', 'Java', 'CSS']);
  const [workSetting, setWorkSetting] = useState('Remote');
  const [salaryRange, setSalaryRange] = useState<[number, number]>([
    149, 14000,
  ]);

  // Ensure salaryRange always has two numbers
  const minSalary = typeof salaryRange[0] === 'number' ? salaryRange[0] : 149;
  const maxSalary = typeof salaryRange[1] === 'number' ? salaryRange[1] : 14000;

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#e6f0ff] to-white py-12 px-2 md:px-0">
      <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Left: Preferences Form */}
        <div className="md:col-span-2 bg-white rounded-3xl shadow p-8 flex flex-col gap-8">
          <h1 className="text-3xl font-bold text-[#374151] mb-2">
            Job Preferences
          </h1>
          {/* Basic Preferences */}
          <section className="bg-[#F8FAFF] rounded-2xl p-6 mb-2">
            <h2 className="font-semibold mb-4">Basic Preferences</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <input
                className="rounded-xl bg-[#F3F6FB] px-4 py-3 w-full"
                placeholder="Job Title / Keywords"
                defaultValue="Senior Software Engineer"
              />
              <select className="rounded-xl bg-[#F3F6FB] px-4 py-3 w-full">
                {categories.map(cat => (
                  <option key={cat}>{cat}</option>
                ))}
              </select>
              <input
                className="rounded-xl bg-[#F3F6FB] px-4 py-3 w-full"
                placeholder="Locations"
                defaultValue="San Francisco, CA"
              />
              <div className="flex flex-col gap-1">
                <label className="text-xs text-gray-500 mb-1">
                  Distance (miles)
                </label>
                <input
                  type="range"
                  min={0}
                  max={100}
                  defaultValue={25}
                  className="w-full accent-blue-500"
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>0</span>
                  <span>25 miles</span>
                  <span>50 miles</span>
                  <span>100 miles</span>
                </div>
              </div>
            </div>
          </section>
          {/* Work Preferences */}
          <section className="bg-[#F8FAFF] rounded-2xl p-6 mb-2">
            <h2 className="font-semibold mb-4">Work Preferences</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <select className="rounded-xl bg-[#F3F6FB] px-4 py-3 w-full">
                {contractTypes.map(type => (
                  <option key={type}>{type}</option>
                ))}
              </select>
              <select className="rounded-xl bg-[#F3F6FB] px-4 py-3 w-full">
                {datePostedOptions.map(opt => (
                  <option key={opt}>{opt}</option>
                ))}
              </select>
            </div>
            <div className="flex gap-4 mb-4">
              {['Remote', 'Hybrid', 'On-site'].map(setting => (
                <button
                  key={setting}
                  className={`px-6 py-2 rounded-full border ${workSetting === setting ? 'bg-[#377DFF] text-white' : 'bg-white text-[#374151]'} font-medium transition`}
                  onClick={() => setWorkSetting(setting)}
                  type="button"
                >
                  {setting}
                </button>
              ))}
            </div>
            <div className="flex flex-col gap-1">
              <label className="text-xs text-gray-500 mb-1">Salary Range</label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min={149}
                  max={14000}
                  value={minSalary}
                  onChange={e => setSalaryRange([+e.target.value, maxSalary])}
                  className="w-full accent-blue-500"
                />
                <input
                  type="range"
                  min={149}
                  max={14000}
                  value={maxSalary}
                  onChange={e => setSalaryRange([minSalary, +e.target.value])}
                  className="w-full accent-blue-500"
                />
              </div>
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>${minSalary.toLocaleString()}</span>
                <span>${maxSalary.toLocaleString()}</span>
              </div>
            </div>
          </section>
          {/* Skills & Technology */}
          <section className="bg-[#F8FAFF] rounded-2xl p-6 mb-2">
            <h2 className="font-semibold mb-4">Skills & Technology</h2>
            <div className="mb-3">
              <input
                className="rounded-xl bg-[#F3F6FB] px-4 py-3 w-full"
                placeholder="Add a skill and press enter"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {skills.map((skill, i) => (
                <span
                  key={i}
                  className="bg-[#E6F0FF] text-[#377DFF] rounded-full px-4 py-1 text-sm flex items-center gap-2"
                >
                  {skill}
                  <button
                    className="ml-1"
                    onClick={() =>
                      setSkills(skills.filter((_, idx) => idx !== i))
                    }
                  >
                    &times;
                  </button>
                </span>
              ))}
            </div>
          </section>
          <div className="flex gap-4 mt-4">
            <button className="border-2 border-[#377DFF] text-[#377DFF] font-semibold rounded-xl px-6 py-3 transition hover:bg-[#e6f0ff]">
              Save Preferences
            </button>
            <button className="bg-[#377DFF] text-white font-semibold rounded-xl px-6 py-3 transition hover:bg-[#2563eb]">
              Save & Search Jobs
            </button>
          </div>
        </div>
        {/* Right: Recent Saved Search */}
        <div className="bg-white rounded-3xl shadow p-8 flex flex-col gap-6">
          <h2 className="text-xl font-bold text-[#374151] mb-1">
            Recent Saved Search
          </h2>
          <p className="text-gray-500 text-sm mb-2">
            Quick access to your favorite job searches
          </p>
          <div className="flex flex-col gap-4">
            {savedSearches.map((search, i) => (
              <div
                key={i}
                className="bg-[#F8FAFF] rounded-2xl p-4 flex flex-col gap-2 shadow-sm"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-semibold text-[#374151]">
                      {search.title}
                    </div>
                    <div className="text-xs text-gray-500">
                      {search.company} • {search.location}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button
                      className="text-[#377DFF] hover:underline"
                      title="Edit"
                    >
                      <svg
                        width="16"
                        height="16"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                      >
                        <path d="M15.232 5.232l3.536 3.536M9 11l6 6M3 21h6l11.293-11.293a1 1 0 0 0 0-1.414l-4.586-4.586a1 1 0 0 0-1.414 0L3 15v6z" />
                      </svg>
                    </button>
                    <button className="text-[#F87171]" title="Delete">
                      <svg
                        width="16"
                        height="16"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                      >
                        <path d="M3 6h18M9 6v12a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V6m-6 0V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2" />
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="flex gap-2 mt-2">
                  <span className="bg-white border border-[#E0E7FF] text-xs rounded-full px-3 py-1 text-[#377DFF]">
                    {search.type}
                  </span>
                  <span className="bg-white border border-[#E0E7FF] text-xs rounded-full px-3 py-1 text-[#377DFF]">
                    {search.salary}
                  </span>
                  <a
                    href="#"
                    className="ml-auto text-[#377DFF] text-xs font-semibold hover:underline"
                  >
                    View Jobs
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
