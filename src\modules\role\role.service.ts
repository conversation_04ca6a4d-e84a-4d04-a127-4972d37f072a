import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Role, RoleDocument } from './schemas/role.schema';

@Injectable()
export class RoleService {
  constructor(
    @InjectModel(Role.name) private readonly roleModel: Model<RoleDocument>,
  ) {}

  async seedRoles() {
    const roles = [
      { name: 'Admin', status: true },
      { name: 'Candidate', status: true, description: 'Candidate role' },
      { name: 'Employee', status: true, description: 'Employee role' },
    ];
    for (const role of roles) {
      const existingRole = await this.roleModel.findOne({ name: role.name });
      if (!existingRole) {
        await this.roleModel.create(role);
      }
    }
    return { message: 'Roles seeded successfully' };
  }

  async findAllExceptAdmin() {
    const roles = await this.roleModel.find({ name: { $ne: 'Admin' } });
    return { message: 'Roles fetched successfully', data: roles };
  }

  async findById(id: string) {
    return this.roleModel.findOne({ _id: id, status: true });
  }
}
