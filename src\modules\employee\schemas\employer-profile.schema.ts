import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type EmployerProfileDocument = EmployerProfile & Document;

@Schema({ timestamps: true })
export class EmployerProfile {
  // Reference to the user collection
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop()
  companyName: string;

  @Prop()
  companyWebsite?: string;

  @Prop({ required: true })
  contactEmail: string;

  @Prop()
  phoneNumber?: string;

  @Prop()
  address?: string;
}

export const EmployerProfileSchema =
  SchemaFactory.createForClass(EmployerProfile);
