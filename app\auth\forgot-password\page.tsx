'use client';

import { ForgotPasswordForm } from '@/components/auth/forgot-password-form';
import { useAppDispatch, useAppSelector } from '@/store';
import { forgotPassword } from '@/store/slices/authSlice';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import Image from 'next/image';
import Link from 'next/link';
import type { ForgotPasswordResponse } from '@/types/auth';

export default function ForgotPasswordPage() {
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(state => state.auth.isLoading);
  const router = useRouter();

  const handleSubmit = async (data: { email: string }) => {
    try {
      const resultAction = await dispatch(forgotPassword(data));
      const res = resultAction.payload as ForgotPasswordResponse;
      if (forgotPassword.fulfilled.match(resultAction)) {
        toast.success(res.message || 'Password reset link sent to email');
        setTimeout(() => {
          router.replace('/auth/login');
        }, 2000);
      } else {
        throw res;
      }
    } catch (err: unknown) {
      if (err && typeof err === 'object' && 'data' in err) {
        toast.error(
          (err as { data?: { message?: string } })?.data?.message ||
            'Failed to send reset link'
        );
      } else {
        toast.error('Failed to send reset link');
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Side */}
      <div className="hidden md:flex flex-col justify-center items-center w-1/2 bg-gradient-to-b from-[#1976F6] to-[#4F8DFD] rounded-tr-[40px] rounded-br-[40px] p-12 relative">
        <h1 className="text-white text-4xl font-extrabold mb-4 text-center drop-shadow-lg">
          Where Top Talents Meet Opportunities
        </h1>
        <p className="text-white text-lg text-center opacity-80 max-w-md">
          TalentLoop connects top candidates with leading employers using
          AI-powered matching. Discover opportunities, streamline hiring, and
          unlock your career potential—all in one platform.
        </p>
      </div>
      {/* Right Side */}
      <div className="flex-1 flex flex-col min-h-screen bg-white">
        {/* Top Bar */}
        <div className="flex items-center justify-between px-10 pt-8">
          <Image
            src="/assets/logo/TalentLoop.svg"
            alt="TalentLoop Logo"
            width={140}
            height={36}
          />
          <nav className="flex gap-8 text-[#1976F6] font-medium text-base">
            <Link href="/">Home</Link>
            <a href="#">About us</a>
            <a href="#">Pricing</a>
            <a href="#">Contact Us</a>
          </nav>
        </div>
        {/* Main Content */}
        <div className="flex flex-1 flex-col justify-center items-center py-8">
          <div className="w-full max-w-md p-8 rounded-2xl shadow-2xl border-0 bg-white">
            <div className="text-3xl font-extrabold text-gray-900 mb-2 text-left">
              Forgot Password
            </div>
            <div className="text-lg text-gray-500 mb-6 text-left">
              {
                "Enter your email address and we'll send you instructions to reset your password."
              }
            </div>

            <ForgotPasswordForm onSubmit={handleSubmit} isLoading={isLoading} />
          </div>
        </div>
      </div>
    </div>
  );
}
