import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  DomainVerification,
  DomainVerificationSchema,
} from './schemas/domain-verification.schema';
import { JobSchema, Job } from './schemas/job.schema';
import { EmployeeService } from './employee.service';
import { EmployeeController } from './employee.controller';
import { MailerModule } from '../mailer/mailer.module';
import {
  EmployerProfile,
  EmployerProfileSchema,
} from './schemas/employer-profile.schema';
import { EmployerProfileService } from './employee.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: DomainVerification.name, schema: DomainVerificationSchema },
      { name: Job.name, schema: JobSchema },
      { name: EmployerProfile.name, schema: EmployerProfileSchema },
    ]),
    MailerModule,
  ],
  controllers: [EmployeeController],
  providers: [EmployeeService, EmployerProfileService],
  exports: [EmployeeService, EmployerProfileService],
})
export class EmployeeModule {}
