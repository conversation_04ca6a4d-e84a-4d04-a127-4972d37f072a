import {
  createCipheriv,
  createDecipheriv,
  randomBytes,
  scrypt,
  createHash,
} from 'crypto';
import { promisify } from 'util';

export class EncryptionUtil {
  private static readonly algorithm = 'aes-256-ctr';
  private static readonly keyLength = 32;
  private static readonly ivLength = 16;

  /**
   * Encrypts the given data using AES-256-CTR
   * @param data The data to encrypt
   * @param secretKey The secret key for encryption
   * @returns The encrypted data in format: iv:encryptedData
   */
  static async encrypt(data: string, secretKey: string): Promise<string> {
    try {
      // Generate a random initialization vector
      const iv = randomBytes(this.ivLength);

      // Generate a key from the secret key
      const key = (await promisify(scrypt)(
        secretKey,
        'salt',
        this.keyLength,
      )) as Buffer;

      // Create cipher
      const cipher = createCipheriv(this.algorithm, key, iv);

      // Encrypt the data
      const encrypted = Buffer.concat([
        cipher.update(data, 'utf8'),
        cipher.final(),
      ]);

      // Combine IV and encrypted data
      return `${iv.toString('hex')}:${encrypted.toString('hex')}`;
    } catch (error) {
      throw new Error(`Encryption failed: ${error}`);
    }
  }

  /**
   * Decrypts the given data using AES-256-CTR
   * @param encryptedData The encrypted data in format: iv:encryptedData
   * @param secretKey The secret key for decryption
   * @returns The decrypted data
   */
  static async decrypt(
    encryptedData: string,
    secretKey: string,
  ): Promise<string> {
    try {
      // Split the encrypted data into IV and actual encrypted data
      const [ivHex, encryptedHex] = encryptedData.split(':');

      // Convert hex strings to buffers
      const iv = Buffer.from(ivHex, 'hex');
      const encrypted = Buffer.from(encryptedHex, 'hex');

      // Generate the same key from the secret key
      const key = (await promisify(scrypt)(
        secretKey,
        'salt',
        this.keyLength,
      )) as Buffer;

      // Create decipher
      const decipher = createDecipheriv(this.algorithm, key, iv);

      // Decrypt the data
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final(),
      ]);

      return decrypted.toString('utf8');
    } catch (error) {
      throw new Error(`Decryption failed: ${error}`);
    }
  }

  /**
   * Encrypts an object by encrypting all its string values
   * @param obj The object to encrypt
   * @param secretKey The secret key for encryption
   * @returns A new object with encrypted values
   */
  static async encryptObject<T extends Record<string, unknown>>(
    obj: T,
    secretKey: string,
  ): Promise<{ [K in keyof T]: T[K] extends string ? string : T[K] }> {
    const encryptedObj = {} as {
      [K in keyof T]: T[K] extends string ? string : T[K];
    };

    for (const key in obj) {
      const value = obj[key];
      if (typeof value === 'string') {
        encryptedObj[key] = (await this.encrypt(value, secretKey)) as T[Extract<
          keyof T,
          string
        >] extends string
          ? string
          : T[Extract<keyof T, string>];
      } else {
        encryptedObj[key] = value as T[Extract<keyof T, string>] extends string
          ? string
          : T[Extract<keyof T, string>];
      }
    }

    return encryptedObj;
  }

  /**
   * Decrypts an object by decrypting all its string values
   * @param obj The object to decrypt
   * @param secretKey The secret key for decryption
   * @returns A new object with decrypted values
   */
  static async decryptObject<T extends Record<string, any>>(
    obj: T,
    secretKey: string,
  ): Promise<T> {
    const decryptedObj = { ...obj };

    for (const key in obj) {
      if (typeof obj[key] === 'string' && (obj[key] as string).includes(':')) {
        decryptedObj[key] = (await this.decrypt(
          obj[key],
          secretKey,
        )) as T[Extract<keyof T, string>];
      }
    }

    return decryptedObj;
  }

  static hash(data: string): string {
    return createHash('sha256').update(data).digest('hex');
  }
}
