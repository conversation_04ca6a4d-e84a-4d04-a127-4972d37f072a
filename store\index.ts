import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import counterSlice from './slices/counterSlice';
import loadingSlice from './slices/loadingSlice';
import rolesReducer from './slices/rolesSlice';
import userActivityReducer from './slices/userActivitySlice';
import authSlice from './slices/authSlice';
import employeeReducer from './slices/employeeSlice';
import candidateProfileReducer from './slices/candidateProfileSlice';
import jobPostReducer from './slices/jobPostSlice';
import skillsSlice from './slices/skillsSlice';

export const store = configureStore({
  reducer: {
    counter: counterSlice,
    loading: loadingSlice,
    auth: authSlice,
    candidateProfile: candidateProfileReducer,
    employee: employeeReducer,
    jobPost: jobPostReducer,
    roles: rolesReducer,
    userActivity: userActivityReducer,
    skills: skillsSlice,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
