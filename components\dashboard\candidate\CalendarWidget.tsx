import React from 'react';

interface Event {
  label: string;
}

export default function CalendarWidget({
  month,
  year,
  events,
}: {
  month: string;
  year: number;
  events: Event[];
}) {
  return (
    <div className="bg-white rounded-2xl shadow p-6">
      <h2 className="font-semibold text-lg mb-4">
        {month} {year}
      </h2>
      <div className="flex flex-col gap-2">
        <div className="grid grid-cols-7 gap-1 text-center text-xs text-gray-400 mb-2">
          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((d, i) => (
            <div key={i}>{d}</div>
          ))}
        </div>
        {/* Simple calendar mockup */}
        <div className="grid grid-cols-7 gap-1 text-center">
          {Array.from({ length: 30 }, (_, i) => (
            <div
              key={i}
              className={`py-1 rounded-full ${i === 1 ? 'bg-[#377DFF] text-white font-bold' : ''}`}
            >
              {i + 1}
            </div>
          ))}
        </div>
        <div className="mt-4">
          <h3 className="text-xs font-semibold mb-1">Upcoming Events</h3>
          <ul className="text-xs text-gray-600">
            {events.map((event, i) => (
              <li key={i}>
                <span className="text-[#377DFF]">●</span> {event.label}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
