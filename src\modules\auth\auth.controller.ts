import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Req,
  Res,
  Headers,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { CreateUserDto } from '../user/dto/create-user.dto';
import { JwtAuthGuard } from './jwt-auth.guard';
import { LoginDto } from './dto/login.dto';
import { ApiResponse } from '../../response.interceptor';
import { ENVIRONMENT } from 'src/utils/constants';
import { ConfigService } from '@nestjs/config';
import { generateCsrfToken } from 'src/utils/helper.util';
import { addToBlocklist } from './token-blocklist';
import { Request, Response } from 'express';
import { User } from '../user/schemas/user.schema';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private configService: ConfigService,
  ) {}

  @Post('login')
  async login(@Body() loginDto: LoginDto): Promise<ApiResponse<any>> {
    return this.authService.login(loginDto);
  }

  @Post('register')
  async register(
    @Body() createUserDto: CreateUserDto,
  ): Promise<ApiResponse<any>> {
    return this.authService.register(createUserDto);
  }

  @Post('admin-login')
  async adminLogin(@Body() loginDto: LoginDto): Promise<ApiResponse<any>> {
    return this.authService.adminLogin(loginDto);
  }

  @Post('forgot-password')
  async forgotPassword(
    @Body('email') email: string,
    @Headers('x-portal-type') portalType: string,
  ): Promise<ApiResponse<any>> {
    return this.authService.forgotPassword(email, portalType);
  }

  @Post('reset-password')
  async resetPassword(
    @Body() body: { token: string; newPassword: string },
  ): Promise<ApiResponse<any>> {
    const { token, newPassword } = body;
    return this.authService.resetPassword(token, newPassword);
  }

  @Post('google/signup')
  async googleSignup(
    @Body() body: { idToken: string; role: string },
  ): Promise<ApiResponse<{ user: User; access_token: string }>> {
    return this.authService.handleGoogleSignup(body.idToken, body.role);
  }

  @Post('google/signin')
  async googleSignin(
    @Body() body: { idToken: string },
  ): Promise<ApiResponse<{ user: User; access_token: string }>> {
    return this.authService.handleGoogleSignin(body.idToken);
  }


  @UseGuards(JwtAuthGuard)
  @Post('logout')
  logout(@Req() req, @Res() res: Response) {
    const authHeader = req.headers['authorization'];
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.slice(7);
      addToBlocklist(token as string);
    }
    return res.status(200).json({ message: 'Logged out successfully' });
  }

  @UseGuards(JwtAuthGuard)
  @Get('user-details')
  async getProfile(@Req() req: Request) {
    const user = (req.user as { userId: string }).userId;
    const response = await this.authService.userDetails(user);
    return {
      message: 'Profile fetched successfully',
      data: response,
      statusCode: 200,
    };
  }

  @Get('/csrf-token')
  createCSRFToken(@Req() req: Request, @Res() res: Response): void {
    console.log(
      ENVIRONMENT.includes(process.env.NODE_ENV),
      '-----||------||-----',
    );
    // Remove old token if exists
    res.clearCookie('XSRF-TOKEN');
    const csrfToken = generateCsrfToken(req, res);
    res.cookie('XSRF-TOKEN', csrfToken, {
      httpOnly: false,
      secure: ENVIRONMENT.includes(this.configService.get<string>('app.env')),
      sameSite: ENVIRONMENT.includes(this.configService.get<string>('app.env'))
        ? 'lax'
        : 'strict',
      domain: this.configService.get<string>('app.domainName'),
    });
    res.json({ data: 'get the csrf-token successfully' });
  }
}
